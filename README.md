# SMP模型优化

本项目对基于GPN和IndRNN的卫星任务规划(SMP)模型进行了一系列优化，旨在提高reward指标。

## 主要改进

### 1. 注意力机制优化
- 实现了更高效的`MultiHead_Additive_Attention`类
- 添加了层归一化和残差连接
- 使用了更好的参数初始化方法（Kaiming初始化）
- 添加了注意力dropout以防止过拟合

### 2. IndRNN网络优化
- 改进了`IndRNN_Net`类，添加了输入输出投影层
- 添加了更有效的残差连接机制
- 增加了层归一化以提高训练稳定性
- 优化了参数初始化方法

### 3. 超参数优化
- 增大了梯度裁剪阈值（从1.0到2.0）
- 略微增加了dropout率（从0.1到0.15）
- 增大了学习率（actor_lr和critic_lr从5e-5到8e-5）
- 增大了批量大小（从8到16）
- 增加了网络层数（从1层到2层）

### 4. 学习率调度优化
- 使用余弦退火学习率调度器替代原有的StepLR
- 实现了学习率周期性重启策略，有助于跳出局部最优
- 为actor和critic分别设置了学习率调度器

## 预期效果
这些优化预计将带来以下改进：
1. 更快的收敛速度
2. 更高的reward值
3. 更好的泛化能力
4. 更稳定的训练过程

## 使用方法
保持原有的训练命令不变，优化已经集成到代码中。 