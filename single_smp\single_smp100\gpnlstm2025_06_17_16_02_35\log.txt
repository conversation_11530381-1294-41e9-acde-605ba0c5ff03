single_smp: 100
model: gpn
rnn: lstm
hidden_size: 256
batch_size: 8
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 1
lr: 0.0001
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 5e-05
critic_lr: 5e-05
2025_06_17_16_02_35
Batch 99/12500, reward: 0.089, revenue_rate: 0.089, distance: 1.538, power: 0.0224, loss: 679.6865, lr:0.0000500, took: 35.8494s
Batch 199/12500, reward: 0.107, revenue_rate: 0.107, distance: 1.849, power: 0.0252, loss: -2.5835, lr:0.0000500, took: 53.3427s
Batch 299/12500, reward: 0.115, revenue_rate: 0.115, distance: 1.962, power: 0.0264, loss: 0.4834, lr:0.0000500, took: 55.6237s
Batch 399/12500, reward: 0.303, revenue_rate: 0.303, distance: 4.764, power: 0.0282, loss: -0.6241, lr:0.0000500, took: 99.9748s
Batch 499/12500, reward: 0.411, revenue_rate: 0.411, distance: 6.211, power: -0.0024, loss: 1.8600, lr:0.0000500, took: 122.1613s
Batch 599/12500, reward: 0.569, revenue_rate: 0.569, distance: 8.423, power: -0.0073, loss: 0.2681, lr:0.0000500, took: 157.4588s
Batch 699/12500, reward: 0.710, revenue_rate: 0.710, distance: 10.738, power: 0.0360, loss: 0.6568, lr:0.0000500, took: 196.6046s
Batch 799/12500, reward: 0.744, revenue_rate: 0.744, distance: 11.478, power: 0.0652, loss: 0.6285, lr:0.0000500, took: 208.1240s
Batch 899/12500, reward: 0.700, revenue_rate: 0.700, distance: 10.894, power: 0.0662, loss: 0.3953, lr:0.0000500, took: 200.3021s
Batch 999/12500, reward: 0.660, revenue_rate: 0.660, distance: 10.125, power: 0.0445, loss: 0.0744, lr:0.0000400, took: 185.7623s
Batch 1099/12500, reward: 0.778, revenue_rate: 0.778, distance: 12.107, power: 0.0785, loss: 1.2251, lr:0.0000400, took: 215.9496s
Batch 1199/12500, reward: 0.812, revenue_rate: 0.812, distance: 12.646, power: 0.0883, loss: 0.2792, lr:0.0000400, took: 227.2382s
Batch 1299/12500, reward: 0.825, revenue_rate: 0.825, distance: 13.005, power: 0.0956, loss: 1.8064, lr:0.0000400, took: 347.2128s
Batch 1399/12500, reward: 0.794, revenue_rate: 0.794, distance: 12.445, power: 0.0966, loss: -0.2187, lr:0.0000400, took: 399.3921s
Batch 1499/12500, reward: 0.786, revenue_rate: 0.786, distance: 12.228, power: 0.0965, loss: 0.8792, lr:0.0000400, took: 395.8502s
Batch 1599/12500, reward: 0.807, revenue_rate: 0.807, distance: 12.789, power: 0.0950, loss: 0.6176, lr:0.0000400, took: 407.8300s
Batch 1699/12500, reward: 0.800, revenue_rate: 0.800, distance: 12.639, power: 0.0949, loss: 0.3304, lr:0.0000400, took: 409.9211s
Batch 1799/12500, reward: 0.792, revenue_rate: 0.792, distance: 12.449, power: 0.1008, loss: 2.1618, lr:0.0000400, took: 400.5915s
Batch 1899/12500, reward: 0.814, revenue_rate: 0.814, distance: 12.691, power: 0.0974, loss: 0.4071, lr:0.0000400, took: 407.3601s
Batch 1999/12500, reward: 0.727, revenue_rate: 0.727, distance: 11.442, power: 0.0957, loss: -0.0015, lr:0.0000320, took: 365.7005s
Batch 2099/12500, reward: 0.762, revenue_rate: 0.762, distance: 11.714, power: 0.0795, loss: 0.5188, lr:0.0000320, took: 375.7603s
Batch 2199/12500, reward: 0.801, revenue_rate: 0.801, distance: 12.652, power: 0.0980, loss: 0.6963, lr:0.0000320, took: 404.2123s
Batch 2299/12500, reward: 0.831, revenue_rate: 0.831, distance: 13.029, power: 0.1022, loss: 0.5286, lr:0.0000320, took: 418.8121s
Batch 2399/12500, reward: 0.842, revenue_rate: 0.842, distance: 13.454, power: 0.1138, loss: 0.1324, lr:0.0000320, took: 428.2853s
Batch 2499/12500, reward: 0.817, revenue_rate: 0.817, distance: 12.918, power: 0.1055, loss: 1.3082, lr:0.0000320, took: 411.2677s
Batch 2599/12500, reward: 0.805, revenue_rate: 0.805, distance: 12.550, power: 0.0910, loss: 0.3807, lr:0.0000320, took: 398.1183s
Batch 2699/12500, reward: 0.849, revenue_rate: 0.849, distance: 13.425, power: 0.1095, loss: 1.6713, lr:0.0000320, took: 425.8964s
Batch 2799/12500, reward: 0.827, revenue_rate: 0.827, distance: 13.106, power: 0.1066, loss: 1.2885, lr:0.0000320, took: 419.6584s
Batch 2899/12500, reward: 0.826, revenue_rate: 0.826, distance: 13.097, power: 0.1088, loss: 1.0716, lr:0.0000320, took: 417.2376s
Batch 2999/12500, reward: 0.827, revenue_rate: 0.827, distance: 13.076, power: 0.1143, loss: 1.1713, lr:0.0000256, took: 417.4470s
Batch 3099/12500, reward: 0.849, revenue_rate: 0.849, distance: 13.464, power: 0.1053, loss: 0.4972, lr:0.0000256, took: 432.0278s
Batch 3199/12500, reward: 0.839, revenue_rate: 0.839, distance: 13.285, power: 0.1113, loss: 0.1126, lr:0.0000256, took: 428.0035s
Batch 3299/12500, reward: 0.819, revenue_rate: 0.819, distance: 12.949, power: 0.1072, loss: -0.0691, lr:0.0000256, took: 415.5964s
Batch 3399/12500, reward: 0.836, revenue_rate: 0.836, distance: 13.133, power: 0.1048, loss: 1.6053, lr:0.0000256, took: 420.5548s
Batch 3499/12500, reward: 0.777, revenue_rate: 0.777, distance: 12.235, power: 0.0982, loss: 1.5734, lr:0.0000256, took: 394.8954s
Batch 3599/12500, reward: 0.829, revenue_rate: 0.829, distance: 13.171, power: 0.1135, loss: 1.4102, lr:0.0000256, took: 425.3526s
Batch 3699/12500, reward: 0.847, revenue_rate: 0.847, distance: 13.474, power: 0.1081, loss: 0.2805, lr:0.0000256, took: 432.0813s
Batch 3799/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.633, power: 0.1144, loss: -0.9957, lr:0.0000256, took: 438.4003s
Batch 3899/12500, reward: 0.813, revenue_rate: 0.813, distance: 12.921, power: 0.1109, loss: 3.5544, lr:0.0000256, took: 412.6296s
Batch 3999/12500, reward: 0.863, revenue_rate: 0.863, distance: 13.896, power: 0.1220, loss: 0.2025, lr:0.0000205, took: 438.9516s
Batch 4099/12500, reward: 0.842, revenue_rate: 0.842, distance: 13.391, power: 0.1132, loss: 1.2032, lr:0.0000205, took: 430.8728s
Batch 4199/12500, reward: 0.813, revenue_rate: 0.813, distance: 12.765, power: 0.1029, loss: 0.7626, lr:0.0000205, took: 411.0723s
Batch 4299/12500, reward: 0.839, revenue_rate: 0.839, distance: 13.227, power: 0.1057, loss: 1.4044, lr:0.0000205, took: 424.0344s
Batch 4399/12500, reward: 0.847, revenue_rate: 0.847, distance: 13.385, power: 0.1076, loss: -0.3566, lr:0.0000205, took: 433.1184s
Batch 4499/12500, reward: 0.858, revenue_rate: 0.858, distance: 13.703, power: 0.1118, loss: 0.6572, lr:0.0000205, took: 437.1181s
Batch 4599/12500, reward: 0.854, revenue_rate: 0.854, distance: 13.692, power: 0.1154, loss: 1.0817, lr:0.0000205, took: 435.8421s
Batch 4699/12500, reward: 0.850, revenue_rate: 0.850, distance: 13.651, power: 0.1173, loss: -0.0965, lr:0.0000205, took: 438.4480s
Batch 4799/12500, reward: 0.866, revenue_rate: 0.866, distance: 13.956, power: 0.1218, loss: 0.9980, lr:0.0000205, took: 443.9322s
Batch 4899/12500, reward: 0.864, revenue_rate: 0.864, distance: 13.880, power: 0.1185, loss: 1.0780, lr:0.0000205, took: 442.7202s
Batch 4999/12500, reward: 0.852, revenue_rate: 0.852, distance: 13.680, power: 0.1149, loss: 2.1612, lr:0.0000164, took: 436.5343s
Batch 5099/12500, reward: 0.862, revenue_rate: 0.862, distance: 13.949, power: 0.1203, loss: 1.2603, lr:0.0000164, took: 445.9091s
Batch 5199/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.736, power: 0.1178, loss: -0.4435, lr:0.0000164, took: 437.9443s
Batch 5299/12500, reward: 0.858, revenue_rate: 0.858, distance: 13.742, power: 0.1165, loss: 0.4287, lr:0.0000164, took: 440.5654s
Batch 5399/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.673, power: 0.1116, loss: 0.4362, lr:0.0000164, took: 437.0196s
Batch 5499/12500, reward: 0.864, revenue_rate: 0.864, distance: 13.844, power: 0.1167, loss: 0.6201, lr:0.0000164, took: 446.0645s
Batch 5599/12500, reward: 0.863, revenue_rate: 0.863, distance: 13.766, power: 0.1116, loss: -0.0064, lr:0.0000164, took: 439.2270s
Batch 5699/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.629, power: 0.1146, loss: 0.2034, lr:0.0000164, took: 435.3384s
Batch 5799/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.689, power: 0.1178, loss: 0.8328, lr:0.0000164, took: 441.9717s
Batch 5899/12500, reward: 0.848, revenue_rate: 0.848, distance: 13.477, power: 0.1142, loss: 1.4848, lr:0.0000164, took: 434.4658s
Batch 5999/12500, reward: 0.858, revenue_rate: 0.858, distance: 13.777, power: 0.1214, loss: 0.2905, lr:0.0000131, took: 441.9216s
Batch 6099/12500, reward: 0.866, revenue_rate: 0.866, distance: 13.961, power: 0.1233, loss: 0.0814, lr:0.0000131, took: 440.9436s
Batch 6199/12500, reward: 0.859, revenue_rate: 0.859, distance: 13.795, power: 0.1230, loss: 0.0458, lr:0.0000131, took: 442.1943s
Batch 6299/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.826, power: 0.1224, loss: 0.5578, lr:0.0000131, took: 441.4273s
Batch 6399/12500, reward: 0.851, revenue_rate: 0.851, distance: 13.698, power: 0.1257, loss: 1.6885, lr:0.0000131, took: 437.1348s
Batch 6499/12500, reward: 0.853, revenue_rate: 0.853, distance: 13.807, power: 0.1243, loss: 1.2648, lr:0.0000131, took: 439.0269s
Batch 6599/12500, reward: 0.848, revenue_rate: 0.848, distance: 13.666, power: 0.1175, loss: 0.4725, lr:0.0000131, took: 437.9676s
Batch 6699/12500, reward: 0.858, revenue_rate: 0.858, distance: 13.736, power: 0.1210, loss: 1.2110, lr:0.0000131, took: 441.5586s
Batch 6799/12500, reward: 0.842, revenue_rate: 0.842, distance: 13.463, power: 0.1202, loss: 0.7102, lr:0.0000131, took: 428.5865s
Batch 6899/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.779, power: 0.1197, loss: 0.4391, lr:0.0000131, took: 441.5071s
Batch 6999/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.766, power: 0.1180, loss: 0.0341, lr:0.0000105, took: 437.7690s
Batch 7099/12500, reward: 0.862, revenue_rate: 0.862, distance: 13.918, power: 0.1200, loss: 0.1451, lr:0.0000105, took: 442.5551s
Batch 7199/12500, reward: 0.853, revenue_rate: 0.853, distance: 13.675, power: 0.1181, loss: 0.4221, lr:0.0000105, took: 436.9943s
Batch 7299/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.673, power: 0.1235, loss: 0.3518, lr:0.0000105, took: 438.6230s
Batch 7399/12500, reward: 0.863, revenue_rate: 0.863, distance: 13.830, power: 0.1196, loss: 0.4809, lr:0.0000105, took: 445.4761s
Batch 7499/12500, reward: 0.845, revenue_rate: 0.845, distance: 13.430, power: 0.1124, loss: 0.6076, lr:0.0000105, took: 433.5929s
Batch 7599/12500, reward: 0.823, revenue_rate: 0.823, distance: 13.119, power: 0.1091, loss: 0.5257, lr:0.0000105, took: 383.4780s
Batch 7699/12500, reward: 0.865, revenue_rate: 0.865, distance: 13.816, power: 0.1200, loss: 0.3767, lr:0.0000105, took: 445.1289s
Batch 7799/12500, reward: 0.870, revenue_rate: 0.870, distance: 14.035, power: 0.1217, loss: 1.2159, lr:0.0000105, took: 446.7730s
Batch 7899/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.803, power: 0.1239, loss: 0.2101, lr:0.0000105, took: 444.8158s
Batch 7999/12500, reward: 0.868, revenue_rate: 0.868, distance: 14.092, power: 0.1281, loss: 0.1356, lr:0.0000084, took: 449.6950s
Batch 8099/12500, reward: 0.865, revenue_rate: 0.865, distance: 14.022, power: 0.1232, loss: 0.7208, lr:0.0000084, took: 450.2168s
Batch 8199/12500, reward: 0.872, revenue_rate: 0.872, distance: 14.126, power: 0.1258, loss: 0.7346, lr:0.0000084, took: 446.7678s
Batch 8299/12500, reward: 0.874, revenue_rate: 0.874, distance: 14.190, power: 0.1291, loss: 0.9911, lr:0.0000084, took: 448.8395s
Batch 8399/12500, reward: 0.863, revenue_rate: 0.863, distance: 13.849, power: 0.1231, loss: 0.1509, lr:0.0000084, took: 445.2494s
Batch 8499/12500, reward: 0.864, revenue_rate: 0.864, distance: 13.908, power: 0.1209, loss: 0.3712, lr:0.0000084, took: 442.8603s
Batch 8599/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.648, power: 0.1166, loss: 0.5176, lr:0.0000084, took: 434.2510s
Batch 8699/12500, reward: 0.871, revenue_rate: 0.871, distance: 14.025, power: 0.1236, loss: 0.4336, lr:0.0000084, took: 446.7301s
Batch 8799/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.819, power: 0.1177, loss: 0.1571, lr:0.0000084, took: 441.1429s
Batch 8899/12500, reward: 0.871, revenue_rate: 0.871, distance: 14.054, power: 0.1200, loss: 0.6602, lr:0.0000084, took: 447.2004s
Batch 8999/12500, reward: 0.865, revenue_rate: 0.865, distance: 13.872, power: 0.1227, loss: 0.6075, lr:0.0000067, took: 446.5151s
Batch 9099/12500, reward: 0.873, revenue_rate: 0.873, distance: 14.077, power: 0.1217, loss: -0.2504, lr:0.0000067, took: 449.5562s
Batch 9199/12500, reward: 0.871, revenue_rate: 0.871, distance: 14.055, power: 0.1235, loss: 0.7386, lr:0.0000067, took: 445.9639s
Batch 9299/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.846, power: 0.1188, loss: 0.4118, lr:0.0000067, took: 444.2663s
Batch 9399/12500, reward: 0.865, revenue_rate: 0.865, distance: 13.858, power: 0.1205, loss: 0.4808, lr:0.0000067, took: 441.0454s
Batch 9499/12500, reward: 0.859, revenue_rate: 0.859, distance: 13.869, power: 0.1174, loss: 0.1754, lr:0.0000067, took: 443.1103s
Batch 9599/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.817, power: 0.1184, loss: 1.3904, lr:0.0000067, took: 446.7401s
Batch 9699/12500, reward: 0.874, revenue_rate: 0.874, distance: 14.041, power: 0.1229, loss: -0.2233, lr:0.0000067, took: 448.8187s
Batch 9799/12500, reward: 0.874, revenue_rate: 0.874, distance: 14.092, power: 0.1232, loss: 0.2043, lr:0.0000067, took: 449.9815s
Batch 9899/12500, reward: 0.869, revenue_rate: 0.869, distance: 14.022, power: 0.1199, loss: 0.3396, lr:0.0000067, took: 449.4843s
Batch 9999/12500, reward: 0.866, revenue_rate: 0.866, distance: 13.962, power: 0.1208, loss: 0.2545, lr:0.0000054, took: 443.7772s
Batch 10099/12500, reward: 0.869, revenue_rate: 0.869, distance: 14.001, power: 0.1256, loss: 0.1443, lr:0.0000054, took: 443.7508s
Batch 10199/12500, reward: 0.868, revenue_rate: 0.868, distance: 13.909, power: 0.1214, loss: 0.7716, lr:0.0000054, took: 444.2717s
Batch 10299/12500, reward: 0.869, revenue_rate: 0.869, distance: 14.083, power: 0.1233, loss: 0.7709, lr:0.0000054, took: 448.2435s
Batch 10399/12500, reward: 0.868, revenue_rate: 0.868, distance: 13.954, power: 0.1214, loss: 0.3294, lr:0.0000054, took: 449.8976s
Batch 10499/12500, reward: 0.862, revenue_rate: 0.862, distance: 13.759, power: 0.1155, loss: 0.3523, lr:0.0000054, took: 442.2401s
Batch 10599/12500, reward: 0.873, revenue_rate: 0.873, distance: 14.006, power: 0.1220, loss: 0.1492, lr:0.0000054, took: 446.0403s
Batch 10699/12500, reward: 0.865, revenue_rate: 0.865, distance: 13.909, power: 0.1195, loss: -0.2451, lr:0.0000054, took: 444.8261s
Batch 10799/12500, reward: 0.872, revenue_rate: 0.872, distance: 14.087, power: 0.1228, loss: -0.2268, lr:0.0000054, took: 447.0582s
Batch 10899/12500, reward: 0.869, revenue_rate: 0.869, distance: 14.029, power: 0.1222, loss: -0.0050, lr:0.0000054, took: 446.6882s
Batch 10999/12500, reward: 0.862, revenue_rate: 0.862, distance: 13.850, power: 0.1209, loss: 0.8548, lr:0.0000043, took: 444.9767s
Batch 11099/12500, reward: 0.867, revenue_rate: 0.867, distance: 14.017, power: 0.1230, loss: 0.1656, lr:0.0000043, took: 447.3833s
Batch 11199/12500, reward: 0.869, revenue_rate: 0.869, distance: 13.987, power: 0.1246, loss: 0.3775, lr:0.0000043, took: 447.4616s
Batch 11299/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.833, power: 0.1210, loss: 0.4593, lr:0.0000043, took: 444.8655s
Batch 11399/12500, reward: 0.862, revenue_rate: 0.862, distance: 13.854, power: 0.1194, loss: 0.3303, lr:0.0000043, took: 444.2029s
Batch 11499/12500, reward: 0.870, revenue_rate: 0.870, distance: 13.996, power: 0.1186, loss: 0.4826, lr:0.0000043, took: 446.2843s
Batch 11599/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.928, power: 0.1223, loss: 1.0986, lr:0.0000043, took: 446.0754s
Batch 11699/12500, reward: 0.874, revenue_rate: 0.874, distance: 14.074, power: 0.1241, loss: -0.0371, lr:0.0000043, took: 445.6098s
Batch 11799/12500, reward: 0.875, revenue_rate: 0.875, distance: 14.107, power: 0.1259, loss: 0.3171, lr:0.0000043, took: 447.4457s
Batch 11899/12500, reward: 0.861, revenue_rate: 0.861, distance: 13.849, power: 0.1240, loss: 0.7609, lr:0.0000043, took: 448.4642s
Batch 11999/12500, reward: 0.870, revenue_rate: 0.870, distance: 13.989, power: 0.1212, loss: 0.2455, lr:0.0000034, took: 446.0040s
Batch 12099/12500, reward: 0.873, revenue_rate: 0.873, distance: 14.043, power: 0.1249, loss: 0.1898, lr:0.0000034, took: 446.0657s
Batch 12199/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.791, power: 0.1222, loss: 0.8964, lr:0.0000034, took: 443.5644s
Batch 12299/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.809, power: 0.1206, loss: -0.0291, lr:0.0000034, took: 443.3928s
Batch 12399/12500, reward: 0.869, revenue_rate: 0.869, distance: 14.040, power: 0.1221, loss: -0.1839, lr:0.0000034, took: 446.0652s
Batch 12499/12500, reward: 0.871, revenue_rate: 0.871, distance: 13.975, power: 0.1212, loss: 0.5187, lr:0.0000034, took: 445.3789s
Epoch 0 mean epoch loss/reward: 5.9808, 0.8163, -0.8777, took: 53067.3592s (406.3646s / 100 batches)
