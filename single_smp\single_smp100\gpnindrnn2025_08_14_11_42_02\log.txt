single_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 32
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.15
actor_lr: 8e-05
critic_lr: 8e-05
2025_08_14_11_42_02
Batch 99/3125, reward: 0.068, revenue_rate: 0.068, distance: 1.170, power: 0.0147, loss: -88.9133, lr:0.0000778, took: 25.1665s
Batch 199/3125, reward: 0.073, revenue_rate: 0.073, distance: 1.254, power: 0.0156, loss: -0.1087, lr:0.0000714, took: 25.6971s
Batch 299/3125, reward: 0.075, revenue_rate: 0.075, distance: 1.315, power: 0.0171, loss: 0.8394, lr:0.0000616, took: 26.5780s
Batch 399/3125, reward: 0.077, revenue_rate: 0.077, distance: 1.324, power: 0.0171, loss: 0.0762, lr:0.0000495, took: 27.1027s
Batch 499/3125, reward: 0.084, revenue_rate: 0.084, distance: 1.431, power: 0.0170, loss: 0.3661, lr:0.0000363, took: 28.5586s
Batch 599/3125, reward: 0.094, revenue_rate: 0.094, distance: 1.537, power: 0.0097, loss: -1.2915, lr:0.0000236, took: 30.9192s
Batch 699/3125, reward: 0.103, revenue_rate: 0.103, distance: 1.598, power: -0.0218, loss: 0.3950, lr:0.0000128, took: 30.2147s
Batch 799/3125, reward: 0.111, revenue_rate: 0.111, distance: 1.588, power: -0.0639, loss: 0.6160, lr:0.0000051, took: 30.7180s
Batch 899/3125, reward: 0.115, revenue_rate: 0.115, distance: 1.589, power: -0.0859, loss: 0.2740, lr:0.0000013, took: 30.8845s
Batch 999/3125, reward: 0.125, revenue_rate: 0.125, distance: 1.581, power: -0.1468, loss: 0.4179, lr:0.0000798, took: 31.1408s
Batch 1099/3125, reward: 0.131, revenue_rate: 0.131, distance: 1.653, power: -0.1561, loss: 1.9822, lr:0.0000785, took: 31.8705s
Batch 1199/3125, reward: 0.137, revenue_rate: 0.137, distance: 1.581, power: -0.2138, loss: -0.5809, lr:0.0000762, took: 33.0768s
Batch 1299/3125, reward: 0.138, revenue_rate: 0.138, distance: 1.697, power: -0.1844, loss: -0.8415, lr:0.0000729, took: 32.8520s
Batch 1399/3125, reward: 0.140, revenue_rate: 0.140, distance: 1.777, power: -0.1596, loss: 1.3752, lr:0.0000687, took: 34.5511s
Batch 1499/3125, reward: 0.142, revenue_rate: 0.142, distance: 1.833, power: -0.1501, loss: -0.0515, lr:0.0000637, took: 35.8028s
Batch 1599/3125, reward: 0.145, revenue_rate: 0.145, distance: 1.729, power: -0.2035, loss: 0.5312, lr:0.0000580, took: 34.2537s
Batch 1699/3125, reward: 0.146, revenue_rate: 0.146, distance: 1.726, power: -0.2196, loss: 0.7116, lr:0.0000519, took: 34.6668s
Batch 1799/3125, reward: 0.146, revenue_rate: 0.146, distance: 1.656, power: -0.2410, loss: 0.1592, lr:0.0000454, took: 35.7607s
Batch 1899/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.671, power: -0.2437, loss: 0.6147, lr:0.0000388, took: 34.6040s
Batch 1999/3125, reward: 0.147, revenue_rate: 0.147, distance: 1.666, power: -0.2467, loss: 0.2095, lr:0.0000322, took: 35.5624s
Batch 2099/3125, reward: 0.149, revenue_rate: 0.149, distance: 1.688, power: -0.2477, loss: 0.4980, lr:0.0000259, took: 36.1802s
Batch 2199/3125, reward: 0.152, revenue_rate: 0.152, distance: 1.739, power: -0.2452, loss: 0.5384, lr:0.0000200, took: 37.0313s
Batch 2299/3125, reward: 0.155, revenue_rate: 0.155, distance: 1.791, power: -0.2339, loss: 0.2205, lr:0.0000146, took: 37.7288s
Batch 2399/3125, reward: 0.160, revenue_rate: 0.160, distance: 1.932, power: -0.2181, loss: 0.3157, lr:0.0000100, took: 40.2887s
Batch 2499/3125, reward: 0.164, revenue_rate: 0.164, distance: 2.017, power: -0.2086, loss: 0.3337, lr:0.0000062, took: 41.9554s
Batch 2599/3125, reward: 0.166, revenue_rate: 0.166, distance: 2.064, power: -0.1994, loss: 0.3873, lr:0.0000034, took: 44.0964s
Batch 2699/3125, reward: 0.167, revenue_rate: 0.167, distance: 2.084, power: -0.1942, loss: 0.0291, lr:0.0000017, took: 41.6283s
Batch 2799/3125, reward: 0.167, revenue_rate: 0.167, distance: 2.083, power: -0.1958, loss: 0.3653, lr:0.0000010, took: 42.9670s
Batch 2899/3125, reward: 0.177, revenue_rate: 0.177, distance: 2.311, power: -0.1635, loss: 0.1808, lr:0.0000799, took: 47.3043s
Batch 2999/3125, reward: 0.187, revenue_rate: 0.187, distance: 2.475, power: -0.1639, loss: -0.6431, lr:0.0000795, took: 50.7188s
Batch 3099/3125, reward: 0.204, revenue_rate: 0.204, distance: 2.798, power: -0.1255, loss: 0.7696, lr:0.0000788, took: 53.7917s
Epoch 0 mean epoch loss/reward: -2.5818, 0.1360, -0.8159, took: 1372.5791s (35.6023s / 100 batches)
Batch 99/3125, reward: 0.264, revenue_rate: 0.264, distance: 3.738, power: -0.1082, loss: -1.5385, lr:0.0000776, took: 66.0729s
Batch 199/3125, reward: 0.339, revenue_rate: 0.339, distance: 4.778, power: -0.1104, loss: -1.7542, lr:0.0000764, took: 76.2218s
Batch 299/3125, reward: 0.349, revenue_rate: 0.349, distance: 5.017, power: -0.0824, loss: 0.9932, lr:0.0000749, took: 80.1505s
Batch 399/3125, reward: 0.354, revenue_rate: 0.354, distance: 5.193, power: -0.0508, loss: 2.4338, lr:0.0000731, took: 80.1696s
Batch 499/3125, reward: 0.391, revenue_rate: 0.391, distance: 5.777, power: -0.0418, loss: 1.0927, lr:0.0000712, took: 88.9128s
Batch 599/3125, reward: 0.423, revenue_rate: 0.423, distance: 6.198, power: -0.0427, loss: -0.4525, lr:0.0000690, took: 92.3886s
Batch 699/3125, reward: 0.457, revenue_rate: 0.457, distance: 6.634, power: -0.0490, loss: 1.0414, lr:0.0000666, took: 101.5744s
Batch 799/3125, reward: 0.481, revenue_rate: 0.481, distance: 7.004, power: -0.0467, loss: 1.3117, lr:0.0000640, took: 107.2011s
Batch 899/3125, reward: 0.504, revenue_rate: 0.504, distance: 7.434, power: -0.0208, loss: 1.6252, lr:0.0000613, took: 112.9686s
Batch 999/3125, reward: 0.527, revenue_rate: 0.527, distance: 7.825, power: -0.0162, loss: 0.6842, lr:0.0000584, took: 112.8454s
Batch 1099/3125, reward: 0.540, revenue_rate: 0.540, distance: 8.036, power: -0.0049, loss: -0.7297, lr:0.0000554, took: 119.5704s
Batch 1199/3125, reward: 0.584, revenue_rate: 0.584, distance: 8.695, power: 0.0045, loss: 0.8540, lr:0.0000522, took: 125.3503s
Batch 1299/3125, reward: 0.566, revenue_rate: 0.566, distance: 8.428, power: 0.0080, loss: 3.4769, lr:0.0000490, took: 123.1024s
Batch 1399/3125, reward: 0.633, revenue_rate: 0.633, distance: 9.386, power: 0.0137, loss: 1.4190, lr:0.0000458, took: 131.0238s
Batch 1499/3125, reward: 0.679, revenue_rate: 0.679, distance: 10.037, power: 0.0253, loss: -0.7453, lr:0.0000425, took: 140.3507s
Batch 1599/3125, reward: 0.690, revenue_rate: 0.690, distance: 10.188, power: 0.0286, loss: 0.2359, lr:0.0000392, took: 141.1310s
Batch 1699/3125, reward: 0.709, revenue_rate: 0.709, distance: 10.485, power: 0.0326, loss: 1.3574, lr:0.0000359, took: 144.3920s
Batch 1799/3125, reward: 0.727, revenue_rate: 0.727, distance: 10.791, power: 0.0427, loss: 0.0996, lr:0.0000326, took: 147.2862s
Batch 1899/3125, reward: 0.734, revenue_rate: 0.734, distance: 10.918, power: 0.0475, loss: 0.5861, lr:0.0000294, took: 147.9266s
Batch 1999/3125, reward: 0.724, revenue_rate: 0.724, distance: 10.818, power: 0.0536, loss: 0.3491, lr:0.0000263, took: 147.1264s
Batch 2099/3125, reward: 0.762, revenue_rate: 0.762, distance: 11.413, power: 0.0618, loss: -0.4763, lr:0.0000232, took: 156.0807s
Batch 2199/3125, reward: 0.736, revenue_rate: 0.736, distance: 11.058, power: 0.0596, loss: -0.1957, lr:0.0000203, took: 149.6420s
Batch 2299/3125, reward: 0.765, revenue_rate: 0.765, distance: 11.463, power: 0.0621, loss: -0.2952, lr:0.0000175, took: 153.1853s
Batch 2399/3125, reward: 0.777, revenue_rate: 0.777, distance: 11.659, power: 0.0700, loss: -0.7316, lr:0.0000149, took: 159.0404s
Batch 2499/3125, reward: 0.781, revenue_rate: 0.781, distance: 11.765, power: 0.0672, loss: -1.1774, lr:0.0000125, took: 157.3553s
Batch 2599/3125, reward: 0.784, revenue_rate: 0.784, distance: 11.792, power: 0.0668, loss: 0.1751, lr:0.0000103, took: 158.0318s
Batch 2699/3125, reward: 0.785, revenue_rate: 0.785, distance: 11.824, power: 0.0694, loss: 0.4992, lr:0.0000082, took: 160.1847s
Batch 2799/3125, reward: 0.789, revenue_rate: 0.789, distance: 11.870, power: 0.0724, loss: 0.5363, lr:0.0000064, took: 160.1109s
Batch 2899/3125, reward: 0.793, revenue_rate: 0.793, distance: 11.956, power: 0.0707, loss: 0.0162, lr:0.0000049, took: 161.5925s
Batch 2999/3125, reward: 0.795, revenue_rate: 0.795, distance: 12.042, power: 0.0714, loss: 0.0042, lr:0.0000036, took: 160.1937s
Batch 3099/3125, reward: 0.796, revenue_rate: 0.796, distance: 11.977, power: 0.0721, loss: -0.2237, lr:0.0000025, took: 161.1799s
Epoch 1 mean epoch loss/reward: -1.1237, 0.3790, -0.8538, took: 4327.3556s (82.6780s / 100 batches)
Batch 99/3125, reward: 0.799, revenue_rate: 0.799, distance: 12.055, power: 0.0752, loss: 0.0933, lr:0.0000016, took: 161.3329s
Batch 199/3125, reward: 0.798, revenue_rate: 0.798, distance: 12.073, power: 0.0752, loss: 0.4031, lr:0.0000012, took: 162.0164s
Batch 299/3125, reward: 0.799, revenue_rate: 0.799, distance: 12.109, power: 0.0746, loss: 0.1479, lr:0.0000010, took: 161.1321s
Batch 399/3125, reward: 0.763, revenue_rate: 0.763, distance: 11.457, power: 0.0653, loss: 2.4484, lr:0.0000800, took: 155.4775s
Batch 499/3125, reward: 0.692, revenue_rate: 0.692, distance: 10.391, power: 0.0487, loss: 2.4056, lr:0.0000799, took: 145.1217s
Batch 599/3125, reward: 0.708, revenue_rate: 0.708, distance: 10.603, power: 0.0469, loss: -0.2295, lr:0.0000797, took: 146.4081s
Batch 699/3125, reward: 0.711, revenue_rate: 0.711, distance: 10.700, power: 0.0574, loss: 1.2961, lr:0.0000795, took: 153.5687s
Batch 799/3125, reward: 0.706, revenue_rate: 0.706, distance: 10.526, power: 0.0462, loss: 0.4007, lr:0.0000792, took: 145.0930s
Batch 899/3125, reward: 0.731, revenue_rate: 0.731, distance: 10.970, power: 0.0547, loss: 1.2500, lr:0.0000788, took: 146.9711s
Batch 999/3125, reward: 0.722, revenue_rate: 0.722, distance: 10.826, power: 0.0533, loss: 1.9276, lr:0.0000784, took: 147.3448s
Batch 1099/3125, reward: 0.747, revenue_rate: 0.747, distance: 11.190, power: 0.0572, loss: -0.0442, lr:0.0000778, took: 152.5660s
Batch 1199/3125, reward: 0.735, revenue_rate: 0.735, distance: 11.065, power: 0.0629, loss: -2.1842, lr:0.0000773, took: 152.8131s
Batch 1299/3125, reward: 0.764, revenue_rate: 0.764, distance: 11.457, power: 0.0610, loss: -0.3201, lr:0.0000766, took: 155.4963s
Batch 1399/3125, reward: 0.761, revenue_rate: 0.761, distance: 11.470, power: 0.0649, loss: -2.2725, lr:0.0000759, took: 154.7103s
Batch 1499/3125, reward: 0.620, revenue_rate: 0.620, distance: 9.419, power: 0.0541, loss: -1.1428, lr:0.0000752, took: 131.7238s
Batch 1599/3125, reward: 0.749, revenue_rate: 0.749, distance: 11.351, power: 0.0696, loss: 3.1502, lr:0.0000744, took: 156.0214s
Batch 1699/3125, reward: 0.720, revenue_rate: 0.720, distance: 11.026, power: 0.0775, loss: -1.5217, lr:0.0000735, took: 151.7761s
Batch 1799/3125, reward: 0.777, revenue_rate: 0.777, distance: 11.871, power: 0.0762, loss: 5.0630, lr:0.0000725, took: 155.5522s
Batch 1899/3125, reward: 0.738, revenue_rate: 0.738, distance: 11.225, power: 0.0692, loss: 2.3750, lr:0.0000715, took: 153.7237s
Batch 1999/3125, reward: 0.739, revenue_rate: 0.739, distance: 11.153, power: 0.0590, loss: -1.8818, lr:0.0000705, took: 150.9342s
Batch 2099/3125, reward: 0.725, revenue_rate: 0.725, distance: 10.909, power: 0.0548, loss: 0.1481, lr:0.0000694, took: 149.6012s
Batch 2199/3125, reward: 0.766, revenue_rate: 0.766, distance: 11.577, power: 0.0649, loss: 3.0308, lr:0.0000682, took: 152.6891s
Batch 2299/3125, reward: 0.780, revenue_rate: 0.780, distance: 11.758, power: 0.0666, loss: -2.0984, lr:0.0000670, took: 156.6791s
Batch 2399/3125, reward: 0.654, revenue_rate: 0.654, distance: 9.874, power: 0.0515, loss: 2.5546, lr:0.0000658, took: 133.3873s
Batch 2499/3125, reward: 0.785, revenue_rate: 0.785, distance: 11.867, power: 0.0741, loss: 0.6532, lr:0.0000645, took: 156.9645s
Batch 2599/3125, reward: 0.772, revenue_rate: 0.772, distance: 11.712, power: 0.0727, loss: 3.8249, lr:0.0000631, took: 154.3742s
Batch 2699/3125, reward: 0.773, revenue_rate: 0.773, distance: 11.743, power: 0.0751, loss: 0.2429, lr:0.0000618, took: 155.2554s
Batch 2799/3125, reward: 0.746, revenue_rate: 0.746, distance: 11.254, power: 0.0674, loss: 0.4605, lr:0.0000604, took: 152.7067s
Batch 2899/3125, reward: 0.764, revenue_rate: 0.764, distance: 11.589, power: 0.0702, loss: -0.2935, lr:0.0000589, took: 162.0425s
Batch 2999/3125, reward: 0.778, revenue_rate: 0.778, distance: 11.778, power: 0.0701, loss: -0.1366, lr:0.0000574, took: 160.3572s
Batch 3099/3125, reward: 0.771, revenue_rate: 0.771, distance: 11.698, power: 0.0743, loss: 1.3081, lr:0.0000559, took: 161.9141s
Epoch 2 mean epoch loss/reward: -0.5250, 0.5011, -0.8628, took: 5040.4457s (106.0407s / 100 batches)
