# 基于改进GPN-IndRNN架构的敏捷对地观测卫星大规模任务规划优化

**Eva Y.-W. Chang<sup>1*</sup>, Rock J.-S. Chern<sup>2</sup>, XXXXXX<sup>3</sup>, 等（如有）**
<sup>1</sup> 浙江大学航空航天学院，杭州，310007，中国（第一作者电话：+86-138-XXXX-XXXX）
<sup>2</sup> 浙江大学航空航天学院，杭州，310007，中国


---

## 摘要

敏捷对地观测卫星任务规划是一个具有挑战性的组合优化问题，需要在多重约束条件下最大化任务收益。本文通过增强编码器、优化多头加性注意力机制和改进训练策略，对现有的GPN+IndRNN框架进行了改进，以解决大规模任务规划问题。该模型包含三个关键改进：（1）具有批量归一化、GELU激活和残差连接的改进编码器；（2）具有层归一化和dropout的优化多头加性注意力机制；（3）通过输入/输出投影、层归一化和残差缩放增强的IndRNN解码器。在多达2000个任务的问题实例上的实验结果表明，与现有方法相比，在大规模问题上取得了显著的性能改进。消融研究证实8个注意力头提供了最佳的性能-效率权衡。所提出的方法为复杂卫星任务规划提供了有效解决方案，可应用于多卫星协调和动态重规划场景。

**关键词：** 卫星任务规划；图指针网络；独立循环神经网络；深度强化学习；组合优化

---

## 符号说明

- $ h_t $：时间 $ t $ 的隐藏状态
- $ i $：任务索引
- $ M_{\text{total}} $：总内存容量
- $ P_i $：任务 $ i $ 的优先级权重
- $ R $：奖励函数
- $ s_i $：任务 $ i $ 的开始时间
- $ e_i $：任务 $ i $ 的结束时间

---

## 缩略语/简称

- **AEOS**：敏捷对地观测卫星
- **GPN**：图指针网络
- **IndRNN**：独立循环神经网络
- **GCN**：图卷积网络
- **PN**：指针网络
- **TSP**：旅行商问题

---

## 1. 引言

敏捷对地观测卫星（AEOS）任务规划是一个NP难的组合优化问题，需要在多重约束下最大化观测收益[1]。传统启发式算法、遗传算法和模拟退火因指数级计算复杂度、有限约束处理能力和缺乏适应性，难以解决大规模问题[2,3]。

深度强化学习在组合优化中显示出前景。指针网络[4]、基于强化学习的TSP解决方案[5]和基于注意力的模型[6]在各个领域都取得了成功。卫星规划的最新应用包括指针网络和图神经网络[7]。Wu等人[8]提出了用于大规模AEOS规划的单头注意力GPN+IndRNN。我们采用这一范式作为骨干，并将贡献重点放在编码器和注意力改进上。

现有方法面临三个主要挑战：训练不稳定性（随着问题规模增加，标准GPN架构表现出严重的梯度消失和不稳定的训练动态）、注意力稀释（传统注意力机制在长序列上遭受注意力权重均匀分布的问题）、序列建模受限（由于梯度不稳定性，传统基于RNN的解码器无法充分建模复杂的时间依赖关系）。我们通过四个关键创新提出了改进的GPN-IndRNN架构：（1）具有批量归一化、GELU激活和残差连接的增强编码器；（2）优化的多头加性注意力机制；（3）通过输入/输出投影、层归一化和残差缩放增强的IndRNN解码器；（4）余弦退火学习率调度。

---

## 2. 问题建模

### 2.1 问题描述

敏捷卫星任务规划涉及从候选任务中选择最优的观测任务序列，同时满足多重约束并最大化总收益。关键特征包括：

1. **时间约束**：每个任务都有严格的观测时间窗口。
2. **空间约束**：卫星姿态调整需要任务间的时间。
3. **资源约束**：有限的星载内存和功率容量限制任务执行。
4. **组合爆炸**：可能的任务组合随任务数量呈指数增长。

### 2.2 数学模型

设 $ T = \{t_1, t_2, \dots, t_n\} $ 表示 $ n $ 个观测任务，每个任务 $ t_i $ 定义为：

$$
t_i = (s_i, e_i, p_i, d_i, r_i, m_i, w_i, f_i) \tag{1}
$$

其中：
- $ s_i $ 和 $ e_i $：时间窗口的开始和结束
- $ p_i $：位置（卫星侧摆角）
- $ d_i $：执行持续时间
- $ r_i $：奖励值
- $ m_i $, $ w_i $：内存和功率消耗
- $ f_i $：地面站指示器

目标是最大化任务奖励率：

$$
\max f(S) = \frac{\sum_{i \in S} r_i}{\sum_{i=1}^{n} r_i} \tag{2}
$$

约束条件：

1. **时间窗口约束**：$ s_i \leq \text{开始时间}_i \leq e_i - d_i, \quad \forall i \in S $ \tag{3}

   每个任务 $ i $ 必须在其时间窗口 $ [s_i, e_i] $ 内开始并在窗口关闭前完成。

2. **姿态机动约束**：$ \text{开始时间}_j \geq \text{开始时间}_i + d_i + \text{机动时间}(p_i, p_j) $ \tag{4}

   任务 $ j $ 不能开始，直到任务 $ i $ 完成且卫星从位置 $ p_i $ 重新定向到 $ p_j $。机动时间为：

   $$\text{机动时间}(p_i, p_j) = \frac{|p_j - p_i|}{\text{角速度}} + \text{稳定时间} \tag{5}$$

   实现参数：角速度 = 0.1 弧度/时间单位，稳定时间 = 0.005 时间单位。

3. **资源约束**：$ \sum_{i \in S} m_i \leq M_{\text{total}}, \quad \sum_{i \in S} w_i \leq W_{\text{total}} $ \tag{6}

   内存容量：$ M_{\text{total}} = 0.3 $ 单位；功率容量：$ W_{\text{total}} = 5.0 $ 单位。
   任务消耗：$ m_i = d_i \times 0.002 $，$ w_i = d_i \times 0.01 $。
   额外机动功率：每次姿态变化 $ 0.01 \times |p_j - p_i| $。

约束通过决策过程中的动态掩码来强制执行。

这个具有 $ O(2^n) $ 解空间复杂度的NP难组合优化问题使得传统精确算法在大规模实例上计算不可行。高维约束空间和稀疏奖励结构带来三个关键挑战：

1. **维度诅咒**：可行解比例从100个任务的1:10³骤降到1000个任务的1:10¹²。
2. **长程依赖挑战**：卫星姿态约束创建了跨越数百个非相邻任务的复杂相互依赖关系。
3. **梯度稀疏性**：稀疏奖励导致策略梯度消失，只有完整的可行序列才能获得显著奖励。

现有的深度学习方法在小规模问题（<500个任务）上有效，但无法扩展到涉及数千个候选任务的现实场景。

---

## 3. 提出的方法

卫星任务规划过程遵循序列决策框架，系统在多重约束下维持可行性的同时，从候选池中迭代选择任务。我们的方法通过对GPN-IndRNN框架的系统性架构增强来解决第1节中识别的三个关键挑战。

**序列决策框架**：在每个决策步骤，模型接收当前系统状态，包括静态任务特征（时间窗口、位置、奖励、资源需求）和动态状态信息（剩余资源、时间可用性、先前选择）。决策过程通过三个关键阶段运行：（1）**增强状态编码**使用具有批量归一化和GELU激活的改进卷积编码器将原始任务属性转换为高维表示；（2）**基于多头注意力的选择**通过优化的加性注意力机制评估任务优先级，该机制捕获多样的依赖模式；（3）**动态状态更新**使用具有残差缩放的增强IndRNN解码器，根据选择的任务修改资源水平、时间约束和可访问性掩码。

**约束处理**：这个迭代过程持续进行，直到没有可行任务剩余或达到资源限制，通过实时掩码防止约束违反，在每一步消除不可行选项。动态掩码机制确保在序列选择过程中严格执行姿态机动约束（方程4）、资源约束（方程6）和时间窗口约束（方程3）。

### 3.1 改进的GPN-IndRNN架构

我们的模型采用Actor-Critic深度强化学习框架，将卫星任务规划建模为序列决策过程。

![图1. 模型结构示意图。](placeholder://fig1)

#### 3.1.1 编码器设计

编码器将原始任务特征映射到高维隐藏表示：

$$
h_i = \text{GELU}(\text{BN}(\text{Conv1d}(x_i))) + \text{Residual}(x_i) \tag{7}
$$

关键改进包括批量归一化[9]以减少内部协变量偏移、GELU激活[10]以获得更平滑的梯度，以及残差连接[11]以缓解梯度消失。这些组件协同工作以改善性能和训练稳定性。

#### 3.1.2 多头加性注意力机制

我们提出了增强的多头加性注意力机制[12]：

$$
\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{f(Q, K)}{\sqrt{d_k}}\right)V \tag{8}
$$
$$
f(Q, K) = v^T \tanh(W_q Q + W_k K + b) \tag{9}
$$

增强功能包括层归一化[13]以稳定隐藏状态表示、注意力dropout[14]以防止过拟合、残差连接以促进梯度流动，以及可学习的缩放因子以动态调整注意力强度。

#### 3.1.3 增强的IndRNN解码器

IndRNN通过为每个神经元分配独立的循环权重来解决传统RNN中的梯度问题：

$$
h_t = \sigma(W_i x_t + u \odot h_{t-1} + b_{ih}) \tag{10}
$$

我们将解码器与基于注意力的指针/瞥见模块耦合。解码器通过以下方式增强：输入/输出投影层以对齐维度、每个IndRNN层后的层归一化、用于更安全深度堆叠的可学习残差缩放因子，以及梯度裁剪策略[15]以防止爆炸。

### 3.2 训练策略

#### 3.2.1 余弦退火学习率调度

我们实现了带有热重启的余弦退火[16]来解决大规模卫星任务规划中的训练不稳定性：

$$
\eta_t = \eta_{\min} + \frac{1}{2}(\eta_{\max} - \eta_{\min})\left(1 + \cos\left(\frac{T_{\text{cur}}}{T_{\max}} \pi\right)\right) \tag{11}
$$

余弦退火调度在 $T_{\max}$ 训练步骤中按余弦曲线在最大值（$\eta_{\max}$）和最小值（$\eta_{\min}$）之间调节学习率 $\eta_t$，其中 $T_{\text{cur}}$ 表示周期内的当前步骤。这种方法结合了乘法周期扩展（$T_{\text{mult}}$）的热重启，允许模型通过周期性重置到更高学习率来逃脱局部最优，同时逐步扩展微调周期。与固定学习率或阶梯式衰减调度不同，余弦退火提供平滑过渡以防止梯度冲击、通过周期性高学习率阶段增强探索，以及通过每个周期内的渐进学习率降低改善收敛。

#### 3.2.2 梯度优化技术

自适应梯度裁剪在保持方向的同时限制梯度范数。参数初始化[17]对卷积和全连接层使用Kaiming初始化，对循环权重使用正交初始化[18]。Adam优化器配置：$ \beta_1 = 0.9 $，$ \beta_2 = 0.999 $，$ \epsilon = 1 \times 10^{-8} $。

#### 3.2.3 多级正则化策略

层特定dropout率：编码器（0.15）、注意力（0.1）、循环层（0.15）。L2权重衰减：$ \lambda = 1 \times 10^{-4} $。如果验证损失连续10个epoch不下降，则早停停止训练。

---

## 4. 实验结果

### 4.1 数据集和实验设置

我们创建了具有现实卫星任务参数的合成数据集。任务数量：每个实例100个任务。训练集：100,000个实例。验证集：10,000个实例。静态特征：8维（时间窗口、位置、持续时间、奖励、内存、功率、地面站）。动态特征：6维（时间窗口状态、可访问性、资源水平、先前任务）。

**比较基线**：GPN-LSTM（带LSTM解码器的图指针网络）和PN-IndRNN（带IndRNN解码器的指针网络）。

所有模型训练参数：隐藏维度 = 256，批量大小 = 32，注意力头 = 8。平台：Windows 10，Intel Core i5-10400F，NVIDIA GeForce RTX 2060。

**主要指标**：奖励率——选定任务奖励总和与所有任务奖励总和的比率。

### 4.2 架构比较

**表1. 不同模型结构的奖励率比较**

| 方法           | 100  | 200  | 300  | 400  | 500  | 750  | 1000 | 1250 | 1500 | 2000 |
|----------------|------|------|------|------|------|------|------|------|------|------|
| GPN+LSTM       | 99.63| 77.47| 54.88| 43.65| 35.44| 25.57| 20.19| 16.73| 14.21| 10.75|
| PN+IndRNN      | 99.68| 81.10| 60.97| 49.66| 42.15| 30.37| 23.90| 19.49| 16.57| 12.55|
| GPN+IndRNN     | 99.21| 82.48| 64.40| 52.48| 44.62| 32.91| 26.27| 20.94| 18.15| 14.24|

我们的方法在小规模问题（100个任务）上取得了竞争性性能，随着问题规模增加显著优于基线方法。

### 4.3 消融研究

**表2. 消融研究**

| 方法                                | 100  | 200  | 300  | 400  | 500  | 750  | 1000 | 1250 | 1500 | 2000 |
|---------------------------------------|------|------|------|------|------|------|------|------|------|------|
| 完整模型（改进编码器 + 多头注意力） | 99.21| 82.48| 64.40| 52.48| 44.62| 32.91| 26.27| 20.94| 18.15| 14.24|
| 卷积编码器 + 多头注意力   | 89.20| 76.17| 58.68| 43.38| 19.39| 5.39 | 3.10 | 2.39 | 1.94 | 1.47 |
| 改进编码器 + 单头注意力 | 97.13| 82.66| 65.90| 53.33| 44.49| 31.84| 25.35| 20.31| 17.55| 13.56|

消融研究验证了我们的设计选择，随着问题规模增加，完整模型与消融变体之间的性能差距显著扩大。编码器稳定化组件对于在大规模深度架构中维持梯度稳定性至关重要，而多头注意力在关键任务依赖关系上保持更集中的注意力模式，能够更好地建模主导大规模卫星任务规划问题的复杂约束交互。

### 4.4 注意力机制分析

**表3. 注意力头数量影响**

| 头数量 | 100  | 200  | 300  | 400  | 500  | 750  | 1000 | 1250 | 1500 | 2000 |
|------------|------|------|------|------|------|------|------|------|------|------|
| 8          | 99.21| 82.48| 64.40| 52.48| 44.62| 32.91| 26.27| 20.94| 18.15| 14.24|
| 2          | 90.24| 77.77| 62.32| 51.26| 43.36| 31.59| 25.23| 20.10| 17.31| 13.51|
| 4          | 98.89| 82.84| 64.70| 52.75| 44.73| 32.86| 25.91| 20.69| 18.03| 14.10|
| 16         | 89.92| 82.69| 65.14| 53.28| 45.12| 33.09| 26.31| 21.11| 18.25| 14.28|

八个头提供了最佳平衡：较少的头无法捕获多样的依赖关系，而更多的头（16）由于每个头的数据有限而引入噪声。大规模问题需要同时建模多种依赖类型（时间、空间、基于资源）。

---

## 5. 结论

本文提出了用于大规模敏捷卫星任务规划的改进GPN-IndRNN架构。主要贡献包括：（1）具有批量归一化、GELU激活和残差连接的增强编码器；（2）8个头提供最佳任务依赖建模的优化多头加性注意力机制；（3）解决梯度问题同时保持强序列建模的改进IndRNN结构；（4）增强训练效率的余弦退火学习率调度。

实验结果表明，与基线方法相比，在大规模问题（多达2000个任务）上具有优越性能。未来工作将专注于理论收敛分析、Transformer架构探索，以及扩展到多卫星协作规划和不确定性下的动态重规划。

---

## 参考文献

[1] Q. Zheng, Y. Cai, and P. Wang, "A modified genetic algorithm for large-scale and joint satellite mission planning," *Egyptian Informatics Journal*, Vol. 31, p. 100713, 2025. https://doi.org/10.1016/j.eij.2025.100713
[2] K. Chen, F.-Y. Zhou, and X.-F. Yuan, "Hybrid particle swarm optimization with spiral-shaped mechanism for feature selection," *Expert Systems with Applications*, Vol. 128, pp. 140–156, 2019. https://doi.org/10.1016/j.eswa.2019.03.039
[3] Z. Zhang, N. Zhang, and Z. Feng, "Multi-satellite control resource scheduling based on ant colony optimization," *Expert Systems with Applications*, Vol. 41, No. 6, pp. 2816–2823, 2014. https://doi.org/10.1016/j.eswa.2013.10.014
[4] O. Vinyals, M. Fortunato, and N. Jaitly, "Pointer networks," in *Advances in Neural Information Processing Systems*, Vol. 28, pp. 2692–2700, 2015.
[5] I. Bello, H. Pham, Q. V. Le, et al., "Neural combinatorial optimization with reinforcement learning," in *Proceedings of the International Conference on Learning Representations (ICLR)*, 2017.
[6] W. Kool, H. van Hoof, and M. Welling, "Attention, learn to solve routing problems!," in *Proceedings of the International Conference on Learning Representations (ICLR)*, 2019.
[7] S. Li, W. Li, C. Cook, Y. Zhu, and C. Shen, "Independently recurrent neural network (IndRNN): Building a longer and deeper RNN," in *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, pp. 5457–5466, 2018.
[8] J. Wu, X. Wang, Z. Shi, F. Zhao, Y. Ma, and Z. Jin, "A large-scale mission planning method for agile earth observation satellite," in *Proceedings of the 2023 35th Chinese Control and Decision Conference (CCDC)*, Yichang, China, 2023, pp. 5012–5017. doi:10.1109/CCDC58219.2023.********.
[9] S. Ioffe and C. Szegedy, "Batch normalization: Accelerating deep network training by reducing internal covariate shift," in *Proceedings of the International Conference on Machine Learning (ICML)*, Vol. 37, pp. 448–456, 2015.
[10] D. Hendrycks and K. Gimpel, "Gaussian error linear units (GELUs)," *arXiv preprint arXiv:1606.08415*, 2016.
[11] K. He, X. Zhang, S. Ren, and J. Sun, "Deep residual learning for image recognition," in *Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR)*, pp. 770–778, 2016.
[12] A. Vaswani, N. Shazeer, N. Parmar, J. Uszkoreit, L. Jones, A. N. Gomez, Ł. Kaiser, and I. Polosukhin, "Attention is all you need," in *Advances in Neural Information Processing Systems*, Vol. 30, pp. 5998–6008, 2017.
[13] J. L. Ba, J. R. Kiros, and G. E. Hinton, "Layer Normalization," arXiv preprint arXiv:1607.06450, 2016.
[14] N. Srivastava, G. Hinton, A. Krizhevsky, I. Sutskever, and R. Salakhutdinov, "Dropout: A simple way to prevent neural networks from overfitting," *Journal of Machine Learning Research*, Vol. 15, No. 1, pp. 1929–1958, 2014.
[15] R. Pascanu, T. Mikolov, and Y. Bengio, "On the difficulty of training recurrent neural networks," in *Proceedings of the International Conference on Machine Learning (ICML)*, Vol. 32, pp. 1310–1318, 2013.
[16] I. Loshchilov and F. Hutter, "SGDR: Stochastic gradient descent with warm restarts," *arXiv preprint arXiv:1608.03983*, 2016.
[17] K. He, X. Zhang, S. Ren, and J. Sun, "Delving deep into rectifiers: Surpassing human-level performance on ImageNet classification," in *Proceedings of the IEEE International Conference on Computer Vision (ICCV)*, pp. 1026–1034, 2015.
[18] A. M. Saxe, J. L. McClelland, and S. Ganguli, "Exact solutions to the nonlinear dynamics of learning in deep linear neural networks," *arXiv preprint arXiv:1312.6120*, 2013.
