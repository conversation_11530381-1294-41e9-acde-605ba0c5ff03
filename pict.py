"""
Defines the main picture for training SMP
"""
import os
import matplotlib.pyplot as plt

def smooth_curve(points, factor=0.8):
    smoothed_points = []
    for point in points:
        if smoothed_points:
            previous = smoothed_points[-1]
            # 上一个节点*0.8+当前节点*0.2
            smoothed_points.append(previous * factor + point * (1 - factor))
        else:
            # 添加point
            smoothed_points.append(point)
    return smoothed_points


def plot_single_smp_train_loss(losses, rewards, critic_rewards, save_dir):
    plt.close('all')
    ax1 = plt.subplot(3, 1, 1)
    ax1.plot(smooth_curve(losses), c='g')
    ax1.set_title('actor loss')
    ax1.set_xlabel('step')
    ax1.set_ylabel('actor loss')

    ax2 = plt.subplot(3, 1, 2)
    ax2.plot(smooth_curve(critic_rewards), c='b')
    ax2.set_xlabel('step')
    ax2.set_ylabel('reward')
    ax2.set_title('critic reward')

    ax3 = plt.subplot(3, 1, 3)
    ax3.plot(smooth_curve(rewards), c='r')
    ax3.set_xlabel('step')
    ax3.set_ylabel('reward')
    ax3.set_title('reward')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, 'train_loss_reward.png'), bbox_inches='tight', dpi=600)  # save picture
