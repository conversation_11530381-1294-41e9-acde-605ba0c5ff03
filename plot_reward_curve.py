#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
敏捷观察卫星任务规划 - 训练Reward曲线绘制脚本
专门用于绘制指定训练结果的reward折线图，支持平滑化处理
"""

import matplotlib.pyplot as plt
import numpy as np
import os
import re

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def parse_log_file(log_path):
    """
    解析训练日志文件，提取reward和loss数据

    Args:
        log_path (str): 日志文件路径

    Returns:
        tuple: (batch_numbers, rewards, losses, epochs) 批次号、奖励值、损失值、轮次信息
    """
    batch_numbers = []
    rewards = []
    losses = []
    epochs = []
    current_epoch = 0

    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        for line in lines:
            line = line.strip()

            # 检测epoch变化
            if line.startswith('Epoch') and 'mean epoch' in line:
                current_epoch += 1
                continue

            # 解析batch数据行
            if line.startswith('Batch'):
                try:
                    # 提取batch号、reward值和loss值
                    batch_match = re.search(r'Batch (\d+)/\d+', line)
                    reward_match = re.search(r'reward: ([\d.-]+)', line)
                    loss_match = re.search(r'loss: ([\d.-]+)', line)

                    if batch_match and reward_match and loss_match:
                        batch_num = int(batch_match.group(1))
                        reward = float(reward_match.group(1))
                        loss = float(loss_match.group(1))

                        batch_numbers.append(batch_num + current_epoch * 3125)  # 考虑epoch偏移
                        rewards.append(reward)
                        losses.append(loss)
                        epochs.append(current_epoch)

                except (ValueError, AttributeError) as e:
                    print(f"解析行时出错: {line}")
                    continue

    except FileNotFoundError:
        print(f"错误: 找不到日志文件 {log_path}")
        return [], [], [], []
    except Exception as e:
        print(f"读取日志文件时出错: {e}")
        return [], [], [], []

    return batch_numbers, rewards, losses, epochs



def smooth_moving_average(data, window_size=10):
    """
    移动平均平滑

    Args:
        data (list): 原始数据
        window_size (int): 窗口大小

    Returns:
        list: 平滑后的数据
    """
    if len(data) < window_size:
        return data

    smoothed = []
    for i in range(len(data)):
        start_idx = max(0, i - window_size // 2)
        end_idx = min(len(data), i + window_size // 2 + 1)
        smoothed.append(np.mean(data[start_idx:end_idx]))
    return smoothed

def smooth_exponential_moving_average(data, alpha=0.1):
    """
    指数移动平均平滑

    Args:
        data (list): 原始数据
        alpha (float): 平滑系数，越小越平滑

    Returns:
        list: 平滑后的数据
    """
    if not data:
        return []

    smoothed = [data[0]]
    for i in range(1, len(data)):
        smoothed_value = alpha * data[i] + (1 - alpha) * smoothed[-1]
        smoothed.append(smoothed_value)
    return smoothed

def smooth_gaussian(data, sigma=2):
    """
    高斯平滑

    Args:
        data (list): 原始数据
        sigma (float): 高斯核的标准差

    Returns:
        list: 平滑后的数据
    """
    if len(data) < 3:
        return data

    # 创建高斯核
    kernel_size = int(6 * sigma + 1)
    if kernel_size % 2 == 0:
        kernel_size += 1

    x = np.arange(kernel_size) - kernel_size // 2
    kernel = np.exp(-0.5 * (x / sigma) ** 2)
    kernel = kernel / np.sum(kernel)

    # 应用卷积
    data_array = np.array(data)
    smoothed = np.convolve(data_array, kernel, mode='same')

    return smoothed.tolist()

def smooth_savitzky_golay(data, window_length=21, polyorder=3):
    """
    Savitzky-Golay滤波平滑

    Args:
        data (list): 原始数据
        window_length (int): 窗口长度，必须为奇数
        polyorder (int): 多项式阶数

    Returns:
        list: 平滑后的数据
    """
    if len(data) < window_length:
        return data

    # 确保window_length为奇数且不超过数据长度
    if window_length % 2 == 0:
        window_length += 1
    window_length = min(window_length, len(data))
    if window_length <= polyorder:
        polyorder = window_length - 1

    try:
        from scipy.signal import savgol_filter
        return savgol_filter(data, window_length, polyorder).tolist()
    except ImportError:
        print("警告: scipy未安装，使用移动平均代替Savitzky-Golay滤波")
        return smooth_moving_average(data, window_length // 3)
    except:
        return data

def apply_multiple_smoothing(data, method='combined'):
    """
    应用多重平滑处理

    Args:
        data (list): 原始数据
        method (str): 平滑方法 ('moving_average', 'exponential', 'gaussian', 'savgol', 'combined')

    Returns:
        list: 平滑后的数据
    """
    if method == 'moving_average':
        return smooth_moving_average(data, window_size=30)
    elif method == 'exponential':
        return smooth_exponential_moving_average(data, alpha=0.05)
    elif method == 'gaussian':
        return smooth_gaussian(data, sigma=3)
    elif method == 'savgol':
        return smooth_savitzky_golay(data, window_length=31, polyorder=3)
    elif method == 'combined':
        # 组合平滑：先指数平滑，再移动平均
        step1 = smooth_exponential_moving_average(data, alpha=0.1)
        step2 = smooth_moving_average(step1, window_size=20)
        return step2
    else:
        return data



def plot_reward_curve(log_path, output_path=None):
    """
    绘制训练reward曲线 - 仅移动平均平滑

    Args:
        log_path (str): 日志文件路径
        output_path (str): 输出图片路径，如果为None则显示图片
    """
    # 解析日志文件
    batch_numbers, rewards, _, _ = parse_log_file(log_path)

    if not rewards:
        print("错误: 没有找到有效的reward数据")
        return

    print(f"成功解析 {len(rewards)} 个数据点")
    print(f"Reward范围: {min(rewards):.4f} - {max(rewards):.4f}")

    # 应用组合平滑处理
    smoothed_rewards = apply_multiple_smoothing(rewards, method='combined')

    # 创建图形
    plt.figure(figsize=(12, 8))

    # 绘制平滑后的曲线
    plt.plot(batch_numbers, smoothed_rewards, 'blue', linewidth=2.5, label='reward', alpha=0.8)
    plt.title('Training Reward Curve', fontsize=16)
    plt.xlabel('Batch', fontsize=14)
    plt.ylabel('Reward', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)

    plt.tight_layout()

    # 保存或显示图片
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"图片已保存到: {output_path}")
    else:
        plt.show()

def get_reference_range(reference_log_path):
    """
    获取参考日志文件的横坐标范围

    Args:
        reference_log_path (str): 参考日志文件路径

    Returns:
        tuple: (min_batch, max_batch) 批次范围
    """
    batch_numbers, _, _, _ = parse_log_file(reference_log_path)
    if batch_numbers:
        return min(batch_numbers), max(batch_numbers)
    else:
        return None, None

def truncate_data_to_range(batch_numbers, rewards, min_batch, max_batch):
    """
    将数据截取到指定的批次范围

    Args:
        batch_numbers (list): 批次号列表
        rewards (list): 奖励值列表
        min_batch (int): 最小批次号
        max_batch (int): 最大批次号

    Returns:
        tuple: (truncated_batch_numbers, truncated_rewards)
    """
    truncated_batches = []
    truncated_rewards = []

    for batch, reward in zip(batch_numbers, rewards):
        if min_batch <= batch <= max_batch:
            truncated_batches.append(batch)
            truncated_rewards.append(reward)

    return truncated_batches, truncated_rewards

def plot_multiple_reward_curves(log_paths, labels, reference_log_path=None, output_path=None):
    """
    绘制多个训练结果的reward曲线对比图，可选择截取到参考文件的横坐标范围

    Args:
        log_paths (list): 日志文件路径列表
        labels (list): 对应的标签列表
        reference_log_path (str): 参考日志文件路径，用于确定横坐标范围
        output_path (str): 输出图片路径，如果为None则显示图片
    """
    plt.figure(figsize=(12, 8))

    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']

    # 获取参考范围
    min_batch, max_batch = None, None
    if reference_log_path and os.path.exists(reference_log_path):
        min_batch, max_batch = get_reference_range(reference_log_path)
        if min_batch is not None and max_batch is not None:
            print(f"参考文件横坐标范围: {min_batch} - {max_batch}")
        else:
            print("警告: 无法从参考文件获取有效范围")

    for i, (log_path, label) in enumerate(zip(log_paths, labels)):
        # 解析日志文件
        batch_numbers, rewards, _, _ = parse_log_file(log_path)

        if not rewards:
            print(f"警告: 文件 {log_path} 没有找到有效的reward数据")
            continue

        # 如果有参考范围，则截取数据
        if min_batch is not None and max_batch is not None:
            original_count = len(batch_numbers)
            batch_numbers, rewards = truncate_data_to_range(batch_numbers, rewards, min_batch, max_batch)
            print(f"成功解析 {label}: {original_count} -> {len(rewards)} 个数据点 (截取到参考范围)")
        else:
            print(f"成功解析 {label}: {len(rewards)} 个数据点")

        if not rewards:
            print(f"警告: {label} 在指定范围内没有数据")
            continue

        print(f"Reward范围: {min(rewards):.4f} - {max(rewards):.4f}")

        # 应用组合平滑处理
        smoothed_rewards = apply_multiple_smoothing(rewards, method='combined')

        # 绘制曲线
        color = colors[i % len(colors)]
        plt.plot(batch_numbers, smoothed_rewards, color=color, linewidth=2.5, label=label, alpha=0.8)

    plt.title('Training Reward Comparison', fontsize=16)
    plt.xlabel('Batch', fontsize=14)
    plt.ylabel('Reward', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)
    plt.tight_layout()

    # 保存或显示图片
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"对比图片已保存到: {output_path}")
    else:
        plt.show()

def main():
    """主函数 - 默认同时绘制reward和loss曲线"""
    # 指定三个训练结果路径
    log_paths = [
        "single_smp/single_smp100/gpnlstm2025_06_17_16_02_35/log.txt",
        "single_smp/single_smp100/pnindrnn2025_06_18_09_35_18/log.txt",
        "single_smp/single_smp100/gpnindrnn2025_08_14_11_42_02/log.txt"
    ]

    # 对应的标签
    labels = [
        "GPN+LSTM",
        "PN+IndRNN",
        "GPN+IndRNN"
    ]

    # 参考文件路径（用于确定横坐标范围）
    reference_log_path = "single_smp/single_smp100/gpnindrnn2025_08_14_11_42_02/log.txt"

    # 检查文件是否存在
    valid_paths = []
    valid_labels = []
    for log_path, label in zip(log_paths, labels):
        if os.path.exists(log_path):
            valid_paths.append(log_path)
            valid_labels.append(label)
            print(f"找到文件: {log_path}")
        else:
            print(f"警告: 找不到日志文件 {log_path}")

    if not valid_paths:
        print("错误: 没有找到任何有效的日志文件")
        return

    # 绘制reward对比曲线
    print("\n开始绘制reward对比曲线...")
    print(f"使用参考文件确定横坐标范围: {reference_log_path}")
    reward_output_path = "training_reward_comparison.png"
    plot_multiple_reward_curves(
        log_paths=valid_paths,
        labels=valid_labels,
        reference_log_path=reference_log_path,
        output_path=reward_output_path
    )

    # 绘制loss对比曲线
    print("\n开始绘制loss对比曲线...")
    loss_output_path = "training_loss_comparison.png"
    plot_loss_curves(
        log_paths=valid_paths,
        labels=valid_labels,
        reference_log_path=reference_log_path,
        output_path=loss_output_path
    )

    print("\n所有图表绘制完成!")

def plot_loss_curves(log_paths, labels, reference_log_path=None, output_path=None):
    """
    绘制多个训练结果的loss曲线对比图

    Args:
        log_paths (list): 日志文件路径列表
        labels (list): 对应的标签列表
        reference_log_path (str): 参考日志文件路径，用于确定横坐标范围
        output_path (str): 输出图片路径，如果为None则显示图片
    """
    plt.figure(figsize=(12, 8))

    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']

    # 获取参考范围
    min_batch, max_batch = None, None
    if reference_log_path and os.path.exists(reference_log_path):
        batch_numbers, _, _, _ = parse_log_file(reference_log_path)
        if batch_numbers:
            min_batch, max_batch = min(batch_numbers), max(batch_numbers)
            print(f"参考文件横坐标范围: {min_batch} - {max_batch}")
        else:
            print("警告: 无法从参考文件获取有效范围")

    for i, (log_path, label) in enumerate(zip(log_paths, labels)):
        # 解析日志文件
        batch_numbers, _, losses, _ = parse_log_file(log_path)

        if not losses:
            print(f"警告: 文件 {log_path} 没有找到有效的loss数据")
            continue

        # 如果有参考范围，则截取数据
        if min_batch is not None and max_batch is not None:
            original_count = len(batch_numbers)
            batch_numbers, losses = truncate_data_to_range(batch_numbers, losses, min_batch, max_batch)
            print(f"成功解析 {label}: {original_count} -> {len(losses)} 个数据点 (截取到参考范围)")
        else:
            print(f"成功解析 {label}: {len(losses)} 个数据点")

        if not losses:
            print(f"警告: {label} 在指定范围内没有数据")
            continue

        print(f"Loss范围: {min(losses):.4f} - {max(losses):.4f}")

        # 应用组合平滑处理
        smoothed_losses = apply_multiple_smoothing(losses, method='combined')

        # 绘制曲线
        color = colors[i % len(colors)]
        plt.plot(batch_numbers, smoothed_losses, color=color, linewidth=2.5, label=label, alpha=0.8)

    plt.title('Training Loss Comparison', fontsize=16)
    plt.xlabel('Batch', fontsize=14)
    plt.ylabel('Loss', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)
    plt.tight_layout()

    # 保存或显示图片
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Loss对比图片已保存到: {output_path}")
    else:
        plt.show()

def main_separate():
    """主函数 - 分别绘制reward和loss曲线"""
    # 指定三个训练结果路径
    log_paths = [
        "single_smp/single_smp100/gpnlstm2025_06_17_16_02_35/log.txt",
        "single_smp/single_smp100/pnindrnn2025_06_18_09_35_18/log.txt",
        "single_smp/single_smp100/gpnindrnn2025_08_14_11_42_02/log.txt"
    ]

    # 对应的标签
    labels = [
        "GPN+LSTM",
        "PN+IndRNN",
        "GPN+IndRNN"
    ]

    # 参考文件路径（用于确定横坐标范围）
    reference_log_path = "single_smp/single_smp100/gpnindrnn2025_08_14_11_42_02/log.txt"

    # 检查文件是否存在
    valid_paths = []
    valid_labels = []
    for log_path, label in zip(log_paths, labels):
        if os.path.exists(log_path):
            valid_paths.append(log_path)
            valid_labels.append(label)
            print(f"找到文件: {log_path}")
        else:
            print(f"警告: 找不到日志文件 {log_path}")

    if not valid_paths:
        print("错误: 没有找到任何有效的日志文件")
        return

    # 绘制reward对比曲线
    print("\n开始绘制reward对比曲线...")
    reward_output_path = "training_reward_comparison.png"
    plot_multiple_reward_curves(
        log_paths=valid_paths,
        labels=valid_labels,
        reference_log_path=reference_log_path,
        output_path=reward_output_path
    )

    # 绘制loss对比曲线
    print("\n开始绘制loss对比曲线...")
    loss_output_path = "training_loss_comparison.png"
    plot_loss_curves(
        log_paths=valid_paths,
        labels=valid_labels,
        reference_log_path=reference_log_path,
        output_path=loss_output_path
    )

    print("\n所有图表绘制完成!")

if __name__ == "__main__":
    main()  # 默认同时绘制reward和loss对比图
