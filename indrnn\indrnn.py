import torch
from torch.nn import Parameter, ParameterList
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable
import numpy as np
import math


def check_bounds(weight, min_abs, max_abs):
    if min_abs:
        abs_kernel = torch.abs(weight).clamp_(min=min_abs)
        weight = torch.mul(torch.sign(weight), abs_kernel)
    if max_abs:
        weight = weight.clamp(max=max_abs, min=-max_abs)
    return weight


class IndRNNCell(nn.Module):
    __constants__ = [
        "hidden_max_abs", "hidden_min_abs", "input_size", "hidden_size",
        "nonlinearity", "hidden_init", "recurrent_init"]

    def __init__(self, input_size, hidden_size, bias=True, nonlinearity="relu",
                 hidden_min_abs=0, hidden_max_abs=None,
                 hidden_init=None, recurrent_init=None,
                 gradient_clip=None):
        super(IndRNNCell, self).__init__()
        self.hidden_max_abs = hidden_max_abs
        self.hidden_min_abs = hidden_min_abs
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.bias = bias
        self.nonlinearity = nonlinearity
        self.hidden_init = hidden_init
        self.recurrent_init = recurrent_init

        if self.nonlinearity == "tanh":
            self.activation = F.tanh
        elif self.nonlinearity == "relu":
            self.activation = F.relu
        elif self.nonlinearity == "gelu":  # 添加GELU激活函数选项
            self.activation = F.gelu
        else:
            raise RuntimeError("Unknown nonlinearity: {}".format(self.nonlinearity))

        self.weight_ih = Parameter(torch.Tensor(hidden_size, input_size))
        self.weight_hh = Parameter(torch.Tensor(hidden_size))
        if bias:
            self.bias_ih = Parameter(torch.Tensor(hidden_size))
        else:
            self.register_parameter('bias_ih', None)  # adds a parameter to the module

        # 添加层归一化
        self.layer_norm = nn.LayerNorm(hidden_size)

        # Clip the gradient
        if gradient_clip:
            if isinstance(gradient_clip, tuple):
                min_g, max_g = gradient_clip
            else:
                max_g = gradient_clip
                min_g = -max_g
            self.weight_ih.register_hook(lambda x: x.clamp(min=min_g, max=max_g))  # register a backward hook
            self.weight_hh.register_hook(lambda x: x.clamp(min=min_g, max=max_g))
            if bias:
                self.bias_ih.register_hook(lambda x: x.clamp(min=min_g, max=max_g))
        self.reset_parameters()

    def check_bounds(self):
        self.weight_hh.data = check_bounds(self.weight_hh.data, self.hidden_min_abs, self.hidden_max_abs)

    def reset_parameters(self):
        # Traverse all parameters
        for name, weight in self.named_parameters():
            if "bias" in name:
                weight.data.zero_()
            elif "weight_hh" in name:
                if self.recurrent_init is None:
                    nn.init.constant_(weight, 1)
                else:
                    self.recurrent_init(weight)
            elif "weight_ih" in name:
                if self.hidden_init is None:
                    # 使用Kaiming初始化替代普通初始化
                    nn.init.kaiming_uniform_(weight, a=math.sqrt(5))
                else:
                    self.hidden_init(weight)
            else:
                weight.data.normal_(0, 0.01)
        # Limited parameter values
        self.check_bounds()

    def forward(self, input, hx):
        # h' = relu(input * (weight_ih) ^ T + hx (*) weight_hh + bias_ih)
        # h' = relu(weight_ih * input + weight_hh (*) hx + bias_ih)

        # weight_ih: (hidden_size, input_size)
        # weight_hh: (hidden_size)
        # bias_ih: (hidden_size)

        # input: (batch_size, input_size)
        # hx: (batch_size, hidden_size)
        # return: (batch_size, hidden_size)
        
        # 计算线性变换
        linear_output = F.linear(input, self.weight_ih, self.bias_ih)
        
        # 计算递归连接
        recurrent_output = torch.mul(self.weight_hh, hx)
        
        # 合并并应用层归一化
        combined = linear_output + recurrent_output
        normalized = self.layer_norm(combined)
        
        # 应用激活函数
        output = self.activation(normalized)
        
        return output


class IndRNN(nn.Module):
    def __init__(self, input_size, hidden_size,
                 n_layer=1, batch_norm=False,
                 batch_first=False, bidirectional=False,
                 hidden_inits=None, recurrent_inits=None,
                 dropout=0.1, attention=False,
                 **kwargs):
        super(IndRNN, self).__init__()
        self.hidden_size = hidden_size
        self.batch_norm = batch_norm
        self.n_layer = n_layer
        self.batch_first = batch_first
        self.bidirectional = bidirectional
        self.num_directions = num_directions = 2 if self.bidirectional else 1
        self.dropout = dropout
        self.attention = attention

        if batch_first:
            self.time_index = 1
            self.batch_index = 0
        else:
            self.time_index = 0
            self.batch_index = 1

        cells = []
        cells_bi = []
        for i in range(n_layer):
            if recurrent_inits is not None:
                kwargs["recurrent_init"] = recurrent_inits[i]
            if hidden_inits is not None:
                kwargs["hidden_init"] = hidden_inits[i]
            in_size = input_size if i == 0 else hidden_size * num_directions
            cells.append(IndRNNCell(in_size, hidden_size, **kwargs))
            cells_bi.append(IndRNNCell(in_size, hidden_size, **kwargs))
        self.cells = nn.ModuleList(cells)
        self.cells_bi = nn.ModuleList(cells_bi)

        # 添加dropout层
        self.dropout_layers = nn.ModuleList([nn.Dropout(dropout) for _ in range(n_layer)])

        if batch_norm:
            bns = []
            for i in range(n_layer):
                bns.append(nn.BatchNorm1d(hidden_size * num_directions))
            self.bns = nn.ModuleList(bns)
            
        # 添加层归一化
        self.layer_norms = nn.ModuleList([nn.LayerNorm(hidden_size * num_directions) for _ in range(n_layer)])
            
        # 添加注意力机制
        if attention:
            self.attention_layers = nn.ModuleList([
                nn.MultiheadAttention(hidden_size * num_directions, 
                                     num_heads=8, 
                                     dropout=dropout) 
                for _ in range(n_layer)
            ])
            
        h0 = torch.zeros(hidden_size * num_directions, requires_grad=False)
        self.register_buffer('h0', h0)  # Adds a persistent buffer to the module

    def forward(self, x, hidden=torch.tensor(float("nan"))):
        batch_norm = self.batch_norm
        time_index = self.time_index
        batch_index = self.batch_index
        num_directions = self.num_directions
        hiddens = []
        i = 0

        for cell in self.cells:
            # x: (batch_size, seq_length, input_size)
            # hx: (batch_size, hidden_size * num_directions)
            hx = self.h0.unsqueeze(0).expand(x.size(batch_index), self.hidden_size * num_directions).contiguous()
            # hx_cell: (batch_size, hidden_size)
            hx_cell = hx[:, : self.hidden_size * 1]
            # hx_cell_bi: (batch_size, hidden_size)
            hx_cell_bi = hx[:, self.hidden_size: self.hidden_size * 2]
            cell.weight_hh.data = check_bounds(cell.weight_hh.data, cell.hidden_min_abs, cell.hidden_max_abs)

            x_n = []
            outputs = []
            # x_T: (seq_length * (batch_size, input_size))
            x_T = torch.unbind(x, time_index)
            time_frame = len(x_T)
            for t in range(time_frame):
                hx_cell = cell(x_T[t], hx_cell)  # recurrent
                outputs.append(hx_cell)
            # x_cell: (batch_size, seq_length, hidden_size)
            x_cell = torch.stack(outputs, time_index)

            if self.bidirectional:
                outputs_bi = []
                for t in range(time_frame - 1, -1, -1):
                    hx_cell_bi = self.cells_bi[i](x_T[t], hx_cell_bi)  # recurrent
                    outputs_bi.append(hx_cell_bi)
                # x_cell_bi: (batch_size, seq_length, hidden_size)
                x_cell_bi = torch.stack(outputs_bi[::-1], time_index)
                # x_cell: (batch_size, seq_length, hidden_size * 2)
                x_cell = torch.cat([x_cell, x_cell_bi], 2)
            # x_n: (batch_size, seq_length, hidden_size * num_directions)
            x_n.append(x_cell)
            hiddens.append(hx_cell)
            # x: (batch_size, seq_length, hidden_size * num_directions)
            x = torch.cat(x_n, -1)
            
            # 应用层归一化
            x = self.layer_norms[i](x)
            
            # 应用注意力机制
            if self.attention:
                # 调整维度以适应多头注意力
                if self.batch_first:
                    x_attn = x.transpose(0, 1)
                else:
                    x_attn = x
                x_attn, _ = self.attention_layers[i](x_attn, x_attn, x_attn)
                if self.batch_first:
                    x_attn = x_attn.transpose(0, 1)
                # 残差连接
                x = x + x_attn
                # 再次应用层归一化
                x = self.layer_norms[i](x)

            if batch_norm:
                if self.batch_first:
                    x = self.bns[i](x.permute(batch_index, 2, time_index).contiguous()).permute(0, 2, 1)
                else:
                    x = self.bns[i](x.permute(batch_index, 2, time_index).contiguous()).permute(2, 0, 1)
                    
            # 应用dropout
            x = self.dropout_layers[i](x)
            i += 1
        return x, torch.cat(hiddens, 1)


class IndRNNv2(nn.Module):
    def __init__(self, input_size, hidden_size,
                 n_layer=1, batch_norm=False,
                 batch_first=False, bidirectional=False,
                 bias=True, hidden_inits=None,
                 recurrent_inits=None, nonlinearity="relu",
                 hidden_min_abs=0, hidden_max_abs=None,
                 gradient_clip=None, dropout=0.1, attention=False):
        super(IndRNNv2, self).__init__()
        self.hidden_size = hidden_size
        self.batch_norm = batch_norm
        self.n_layer = n_layer
        self.batch_first = batch_first
        self.bidirectional = bidirectional
        self.nonlinearity = nonlinearity
        self.hidden_min_abs = hidden_min_abs
        self.hidden_max_abs = hidden_max_abs
        self.gradient_clip = gradient_clip
        self.dropout = dropout
        self.attention = attention

        if gradient_clip:
            if isinstance(gradient_clip, tuple):
                min_g, max_g = gradient_clip
            else:
                max_g = gradient_clip
                min_g = -max_g

        if self.nonlinearity == "tanh":
            self.activation = F.tanh
        elif self.nonlinearity == "relu":
            self.activation = F.relu
        elif self.nonlinearity == "gelu":  # 添加GELU激活函数选项
            self.activation = F.gelu
        else:
            raise RuntimeError("Unknown nonlinearity: {}".format(self.nonlinearity))

        self.num_directions = num_directions = 2 if self.bidirectional else 1
        if batch_first:
            self.time_index = 1
            self.batch_index = 0
        else:
            self.time_index = 0
            self.batch_index = 1

        self.cells_recurrent = ParameterList(
            [Parameter(torch.Tensor(num_directions * hidden_size)) for i in range(n_layer)])
        if gradient_clip:
            for param in self.cells_recurrent:
                param.register_hook(lambda x: x.clamp(min=min_g, max=max_g))

        cells_hidden = []
        for i in range(n_layer):
            directions_hidden = []
            in_size = input_size * num_directions if i == 0 else hidden_size * num_directions ** 2
            hidden = nn.Conv1d(in_size, hidden_size * num_directions, 1, groups=num_directions)
            if hidden_inits is not None:
                hidden_inits[i](hidden.weight)
            else:
                # 使用Kaiming初始化
                torch.nn.init.kaiming_uniform_(hidden.weight, a=math.sqrt(5))
            if bias:
                torch.nn.init.constant_(hidden.bias, 0)
                if gradient_clip:
                    hidden.bias.register_hook(lambda x: x.clamp(min=min_g, max=max_g))
            if recurrent_inits is not None:
                recurrent_inits[i](self.cells_recurrent[i])
            else:
                torch.nn.init.constant_(self.cells_recurrent[i], 1)
            hidden.weight.data = check_bounds(hidden.weight.data, self.hidden_min_abs, self.hidden_max_abs)
            if gradient_clip:
                hidden.weight.register_hook(lambda x: x.clamp(min=min_g, max=max_g))
            cells_hidden.append(hidden)
        self.cells_hidden = nn.ModuleList(cells_hidden)

        # 添加dropout层
        self.dropout_layers = nn.ModuleList([nn.Dropout(dropout) for _ in range(n_layer)])
        
        # 添加层归一化
        self.layer_norms = nn.ModuleList([nn.LayerNorm(hidden_size * num_directions) for _ in range(n_layer)])
        
        # 添加注意力机制
        if attention:
            self.attention_layers = nn.ModuleList([
                nn.MultiheadAttention(hidden_size * num_directions, 
                                     num_heads=8, 
                                     dropout=dropout) 
                for _ in range(n_layer)
            ])

        if batch_norm:
            bns = []
            for i in range(n_layer):
                bns.append(nn.BatchNorm1d(hidden_size * num_directions))
            self.bns = nn.ModuleList(bns)
        h0 = torch.zeros(hidden_size * num_directions, requires_grad=False)
        self.register_buffer('h0', h0)

    def forward(self, x, hidden=None):
        # x: (batch_size, seq_length, input_size)
        batch_norm = self.batch_norm
        time_index = self.time_index
        batch_index = self.batch_index
        num_directions = self.num_directions
        frame_size = x.size(self.time_index)
        batch_size = x.size(self.batch_index)
        # x: (batch_size, input_size, seq_length)
        x = x.permute(self.batch_index, -1, self.time_index)
        hiddens = []
        i = 0
        for cell_hidden in self.cells_hidden:
            cell_hidden.weight.data = check_bounds(cell_hidden.weight.data, self.hidden_min_abs, self.hidden_max_abs)
            if hidden is None:
                # hx: (batch_size, hidden_size * num_directions)
                hx = self.h0.unsqueeze(0).expand(batch_size, self.hidden_size * num_directions).contiguous()
            else:
                hx = hidden[:, (i * self.hidden_size * num_directions): ((i + 1) * self.hidden_size * num_directions)]
            outputs = []
            # x_T: (batch_size, input_size * num_directions, seq_length)
            if self.bidirectional:
                x_T = torch.cat([x, x.flip(-1)], 1)
            else:
                x_T = x
            # lin: (batch_size, hidden_size * num_directions, seq_length)
            lin = cell_hidden(x_T)
            # lin: (seq_length * (batch_size, hidden_size * num_directions))
            lin = torch.unbind(lin, 2)
            # recurrent_h: (hidden_size * num_directions)
            recurrent_h = self.cells_recurrent[i]
            for t in range(frame_size):
                # hx: (batch_size, hidden_size * num_directions)
                hx = self.activation(lin[t] + torch.mul(recurrent_h, hx))
                outputs.append(hx)
            # x: (batch_size, hidden_size * num_directions, seq_length)
            x = torch.stack(outputs, 2)
            hiddens.append(hx)
            
            # 应用层归一化（需要调整维度）
            x_norm = x.permute(0, 2, 1)  # (batch_size, seq_length, hidden_size * num_directions)
            x_norm = self.layer_norms[i](x_norm)
            x = x_norm.permute(0, 2, 1)  # 变回原来的形状
            
            # 应用注意力机制
            if self.attention:
                # 调整维度以适应多头注意力
                x_attn = x.permute(2, 0, 1)  # (seq_length, batch_size, hidden_size * num_directions)
                x_attn, _ = self.attention_layers[i](x_attn, x_attn, x_attn)
                x_attn = x_attn.permute(1, 2, 0)  # 变回原来的形状
                # 残差连接
                x = x + x_attn
                # 再次应用层归一化
                x_norm = x.permute(0, 2, 1)
                x_norm = self.layer_norms[i](x_norm)
                x = x_norm.permute(0, 2, 1)
            
            if batch_norm:
                if self.batch_first:
                    x = self.bns[i](x)
                else:
                    x = self.bns[i](x)
                    
            # 应用dropout，但不应用于最后一层
            if i < self.n_layer - 1:
                x = self.dropout_layers[i](x.permute(0, 2, 1)).permute(0, 2, 1)
                
            i += 1
        # hiddens: (batch_size, hidden_size * num_directions * seq_length)
        hiddens = torch.cat(hiddens, -1)
        if self.batch_first:
            x = x.permute(0, 2, 1)
        else:
            x = x.permute(2, 0, 1)
        # x: (batch_size, seq_length, hidden_size * num_directions)
        # hiddens: (batch_size, hidden_size * num_layers * num_directions)
        return x.squeeze(2), hiddens


class IndRNN_Net(nn.Module):
    def __init__(self, input_size, hidden_size, num_nodes, n_layer=2, model=IndRNN):
        super(IndRNN_Net, self).__init__()
        self.n_layer = n_layer
        self.hidden_size = hidden_size
        self.num_nodes = num_nodes
        
        # 添加输入投影层
        self.input_proj = nn.Linear(input_size, hidden_size)
        self.input_norm = nn.LayerNorm(hidden_size)
        
        # 使用IndRNN作为主要处理单元
        self.indrnn = model(hidden_size, hidden_size, n_layer=n_layer, 
                           batch_first=True, bidirectional=False, 
                           dropout=0.1, attention=True)
        
        # 添加输出投影层
        self.output_proj = nn.Linear(hidden_size, hidden_size)
        self.output_norm = nn.LayerNorm(hidden_size)
        
        # 添加残差连接的缩放因子
        self.res_scale = nn.Parameter(torch.ones(1))
        
        # 初始化参数
        for name, param in self.named_parameters():
            if 'weight' in name and len(param.shape) > 1:
                nn.init.kaiming_uniform_(param, a=math.sqrt(5))
            elif 'bias' in name:
                nn.init.constant_(param, 0)

    def forward(self, x, hidden=None):
        # x: (batch_size, seq_length, input_size)
        # hiddens: (batch_size, hidden_size * num_layers)
        # y: (batch_size, seq_length, hidden_size * num_directions)
        
        # 保存输入用于残差连接
        identity = x
        
        # 输入投影
        if x.size(-1) != self.hidden_size:
            x = self.input_proj(x)
            x = self.input_norm(x)
        
        # 通过IndRNN处理
        y, hiddens = self.indrnn(x, hidden)
        
        # 输出投影
        y = self.output_proj(y)
        y = self.output_norm(y)
        
        # 残差连接（如果输入和输出维度匹配）
        if identity.size(-1) == y.size(-1):
            y = y + self.res_scale * identity
            y = self.output_norm(y)
        
        return y, hiddens


def testIndRNNCell():
    x = Variable(torch.Tensor([[1., 1., 1., 1.]]))
    m = Variable(torch.Tensor([[2., 2., 2., 2.]]))
    recurrent_init = torch.Tensor([-5., -2., 0.1, 5.])
    cell = IndRNNCell(4, 4, hidden_min_abs=1., hidden_max_abs=3.)
    cell.weight_ih.data.fill_(1)
    cell.weight_hh.data.copy_(recurrent_init)
    cell.check_bounds()
    output = cell(x, m)

    # Recurrent Weights u should be -3, -2, 1, 3
    # Pre-activations (4 + 2 * u) should be -2, 0, 6, 10
    np.testing.assert_array_equal(output.data.numpy(), [[0., 0., 6., 10.]])
    print(output)


if __name__ == "__main__":
    testIndRNNCell()
