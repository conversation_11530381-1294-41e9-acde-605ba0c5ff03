<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="PublishConfigData" autoUpload="Always" serverName="cyf@10.12.34.117:22 password (9)">
    <serverData>
      <paths name="cyf@10.12.34.117:22 password">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="cyf@10.12.34.117:22 password (2)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="cyf@10.12.34.117:22 password (3)">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/pycharm/0613" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="cyf@10.12.34.117:22 password (4)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="cyf@10.12.34.117:22 password (5)">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/pycharm/0616" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="cyf@10.12.34.117:22 password (6)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="cyf@10.12.34.117:22 password (7)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="cyf@10.12.34.117:22 password (8)">
        <serverdata>
          <mappings>
            <mapping local="$PROJECT_DIR$" web="/" />
          </mappings>
        </serverdata>
      </paths>
      <paths name="cyf@10.12.34.117:22 password (9)">
        <serverdata>
          <mappings>
            <mapping deploy="/home/<USER>/pycharm/0617" local="$PROJECT_DIR$" />
          </mappings>
        </serverdata>
      </paths>
    </serverData>
    <option name="myAutoUpload" value="ALWAYS" />
  </component>
</project>