single_smp: 100
model: pn
rnn: indrnn
hidden_size: 256
batch_size: 8
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 1
lr: 0.0001
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 5e-05
critic_lr: 5e-05
2025_06_18_09_35_18
Batch 99/12500, reward: 0.079, revenue_rate: 0.079, distance: 1.350, power: 0.0119, loss: -26.6344, lr:0.0000500, took: 27.6491s
Batch 199/12500, reward: 0.080, revenue_rate: 0.080, distance: 1.360, power: 0.0156, loss: 0.5175, lr:0.0000500, took: 6.5090s
Batch 299/12500, reward: 0.078, revenue_rate: 0.078, distance: 1.348, power: 0.0196, loss: -0.2721, lr:0.0000500, took: 6.5262s
Batch 399/12500, reward: 0.080, revenue_rate: 0.080, distance: 1.367, power: 0.0174, loss: 0.2753, lr:0.0000500, took: 8.0975s
Batch 499/12500, reward: 0.079, revenue_rate: 0.079, distance: 1.346, power: 0.0157, loss: 0.4639, lr:0.0000500, took: 7.7454s
Batch 599/12500, reward: 0.082, revenue_rate: 0.082, distance: 1.382, power: 0.0149, loss: 1.1132, lr:0.0000500, took: 6.9455s
Batch 699/12500, reward: 0.084, revenue_rate: 0.084, distance: 1.439, power: 0.0141, loss: 0.7105, lr:0.0000500, took: 6.7608s
Batch 799/12500, reward: 0.084, revenue_rate: 0.084, distance: 1.398, power: 0.0195, loss: 1.0572, lr:0.0000500, took: 6.7825s
Batch 899/12500, reward: 0.088, revenue_rate: 0.088, distance: 1.499, power: 0.0175, loss: 1.2442, lr:0.0000500, took: 6.7990s
Batch 999/12500, reward: 0.098, revenue_rate: 0.098, distance: 1.639, power: 0.0151, loss: 1.0278, lr:0.0000400, took: 7.2459s
Batch 1099/12500, reward: 0.114, revenue_rate: 0.114, distance: 1.885, power: 0.0109, loss: 0.4821, lr:0.0000400, took: 8.2188s
Batch 1199/12500, reward: 0.137, revenue_rate: 0.137, distance: 2.220, power: 0.0054, loss: 0.7802, lr:0.0000400, took: 9.3086s
Batch 1299/12500, reward: 0.175, revenue_rate: 0.175, distance: 2.749, power: -0.0020, loss: 1.8603, lr:0.0000400, took: 11.2910s
Batch 1399/12500, reward: 0.235, revenue_rate: 0.235, distance: 3.431, power: -0.0639, loss: 0.3502, lr:0.0000400, took: 13.5074s
Batch 1499/12500, reward: 0.317, revenue_rate: 0.317, distance: 4.500, power: -0.1059, loss: 0.0687, lr:0.0000400, took: 16.5886s
Batch 1599/12500, reward: 0.417, revenue_rate: 0.417, distance: 5.888, power: -0.0946, loss: 3.2139, lr:0.0000400, took: 21.1352s
Batch 1699/12500, reward: 0.508, revenue_rate: 0.508, distance: 7.369, power: -0.0578, loss: 0.4123, lr:0.0000400, took: 25.0696s
Batch 1799/12500, reward: 0.603, revenue_rate: 0.603, distance: 8.852, power: -0.0195, loss: 1.1734, lr:0.0000400, took: 29.3102s
Batch 1899/12500, reward: 0.666, revenue_rate: 0.666, distance: 9.976, power: 0.0100, loss: -2.3569, lr:0.0000400, took: 31.6758s
Batch 1999/12500, reward: 0.714, revenue_rate: 0.714, distance: 10.779, power: 0.0305, loss: 2.5203, lr:0.0000320, took: 33.5311s
Batch 2099/12500, reward: 0.737, revenue_rate: 0.737, distance: 11.246, power: 0.0453, loss: 0.2821, lr:0.0000320, took: 35.4168s
Batch 2199/12500, reward: 0.752, revenue_rate: 0.752, distance: 11.522, power: 0.0546, loss: 1.1169, lr:0.0000320, took: 36.3381s
Batch 2299/12500, reward: 0.767, revenue_rate: 0.767, distance: 11.734, power: 0.0605, loss: 2.4954, lr:0.0000320, took: 37.8547s
Batch 2399/12500, reward: 0.776, revenue_rate: 0.776, distance: 11.986, power: 0.0694, loss: 0.7436, lr:0.0000320, took: 38.5998s
Batch 2499/12500, reward: 0.793, revenue_rate: 0.793, distance: 12.354, power: 0.0792, loss: 0.7982, lr:0.0000320, took: 36.9507s
Batch 2599/12500, reward: 0.798, revenue_rate: 0.798, distance: 12.410, power: 0.0856, loss: 1.5668, lr:0.0000320, took: 34.8333s
Batch 2699/12500, reward: 0.804, revenue_rate: 0.804, distance: 12.542, power: 0.0837, loss: 0.4230, lr:0.0000320, took: 38.9458s
Batch 2799/12500, reward: 0.811, revenue_rate: 0.811, distance: 12.703, power: 0.0881, loss: 0.4934, lr:0.0000320, took: 40.7063s
Batch 2899/12500, reward: 0.817, revenue_rate: 0.817, distance: 12.811, power: 0.0914, loss: 1.2139, lr:0.0000320, took: 41.5588s
Batch 2999/12500, reward: 0.818, revenue_rate: 0.818, distance: 12.776, power: 0.0901, loss: -1.0584, lr:0.0000256, took: 39.7251s
Batch 3099/12500, reward: 0.821, revenue_rate: 0.821, distance: 12.919, power: 0.0929, loss: -2.6005, lr:0.0000256, took: 40.0403s
Batch 3199/12500, reward: 0.820, revenue_rate: 0.820, distance: 12.931, power: 0.0948, loss: -0.7261, lr:0.0000256, took: 40.5207s
Batch 3299/12500, reward: 0.828, revenue_rate: 0.828, distance: 13.117, power: 0.0978, loss: -2.5882, lr:0.0000256, took: 43.6833s
Batch 3399/12500, reward: 0.827, revenue_rate: 0.827, distance: 13.126, power: 0.1010, loss: 2.3994, lr:0.0000256, took: 41.7011s
Batch 3499/12500, reward: 0.832, revenue_rate: 0.832, distance: 13.150, power: 0.1042, loss: -2.2051, lr:0.0000256, took: 42.5870s
Batch 3599/12500, reward: 0.827, revenue_rate: 0.827, distance: 13.091, power: 0.0994, loss: 3.6584, lr:0.0000256, took: 41.0416s
Batch 3699/12500, reward: 0.828, revenue_rate: 0.828, distance: 13.107, power: 0.0983, loss: 0.2263, lr:0.0000256, took: 40.9884s
Batch 3799/12500, reward: 0.833, revenue_rate: 0.833, distance: 13.317, power: 0.1026, loss: 2.9045, lr:0.0000256, took: 42.6981s
Batch 3899/12500, reward: 0.837, revenue_rate: 0.837, distance: 13.275, power: 0.1041, loss: 1.3173, lr:0.0000256, took: 41.1606s
Batch 3999/12500, reward: 0.840, revenue_rate: 0.840, distance: 13.407, power: 0.1038, loss: -1.7216, lr:0.0000205, took: 43.0728s
Batch 4099/12500, reward: 0.836, revenue_rate: 0.836, distance: 13.245, power: 0.1037, loss: -0.2498, lr:0.0000205, took: 41.5261s
Batch 4199/12500, reward: 0.835, revenue_rate: 0.835, distance: 13.308, power: 0.1043, loss: -0.0168, lr:0.0000205, took: 42.0533s
Batch 4299/12500, reward: 0.839, revenue_rate: 0.839, distance: 13.345, power: 0.1059, loss: -2.9541, lr:0.0000205, took: 37.4971s
Batch 4399/12500, reward: 0.835, revenue_rate: 0.835, distance: 13.279, power: 0.1069, loss: 0.0797, lr:0.0000205, took: 37.4356s
Batch 4499/12500, reward: 0.837, revenue_rate: 0.837, distance: 13.307, power: 0.1059, loss: 0.4564, lr:0.0000205, took: 38.4570s
Batch 4599/12500, reward: 0.843, revenue_rate: 0.843, distance: 13.491, power: 0.1060, loss: -1.8424, lr:0.0000205, took: 39.2475s
Batch 4699/12500, reward: 0.841, revenue_rate: 0.841, distance: 13.398, power: 0.1087, loss: 1.4598, lr:0.0000205, took: 42.3821s
Batch 4799/12500, reward: 0.846, revenue_rate: 0.846, distance: 13.537, power: 0.1104, loss: -0.2822, lr:0.0000205, took: 43.0322s
Batch 4899/12500, reward: 0.840, revenue_rate: 0.840, distance: 13.363, power: 0.1073, loss: 2.2961, lr:0.0000205, took: 43.1311s
Batch 4999/12500, reward: 0.845, revenue_rate: 0.845, distance: 13.516, power: 0.1064, loss: 1.0382, lr:0.0000164, took: 45.2690s
Batch 5099/12500, reward: 0.846, revenue_rate: 0.846, distance: 13.490, power: 0.1081, loss: 0.0757, lr:0.0000164, took: 43.9871s
Batch 5199/12500, reward: 0.848, revenue_rate: 0.848, distance: 13.573, power: 0.1114, loss: -1.4561, lr:0.0000164, took: 46.4580s
Batch 5299/12500, reward: 0.840, revenue_rate: 0.840, distance: 13.473, power: 0.1078, loss: -1.3166, lr:0.0000164, took: 43.8569s
Batch 5399/12500, reward: 0.844, revenue_rate: 0.844, distance: 13.472, power: 0.1092, loss: -0.6949, lr:0.0000164, took: 40.8191s
Batch 5499/12500, reward: 0.844, revenue_rate: 0.844, distance: 13.505, power: 0.1116, loss: -0.0489, lr:0.0000164, took: 45.2694s
Batch 5599/12500, reward: 0.847, revenue_rate: 0.847, distance: 13.602, power: 0.1112, loss: 0.3157, lr:0.0000164, took: 38.8490s
Batch 5699/12500, reward: 0.845, revenue_rate: 0.845, distance: 13.528, power: 0.1134, loss: -0.3757, lr:0.0000164, took: 38.5528s
Batch 5799/12500, reward: 0.852, revenue_rate: 0.852, distance: 13.546, power: 0.1140, loss: 0.5491, lr:0.0000164, took: 43.2516s
Batch 5899/12500, reward: 0.848, revenue_rate: 0.848, distance: 13.589, power: 0.1116, loss: -0.4326, lr:0.0000164, took: 196.5284s
Batch 5999/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.745, power: 0.1131, loss: -0.4168, lr:0.0000131, took: 217.1086s
Batch 6099/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.814, power: 0.1150, loss: 0.8427, lr:0.0000131, took: 216.9349s
Batch 6199/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.756, power: 0.1138, loss: 2.0406, lr:0.0000131, took: 217.4252s
Batch 6299/12500, reward: 0.852, revenue_rate: 0.852, distance: 13.649, power: 0.1129, loss: 0.8711, lr:0.0000131, took: 218.0844s
Batch 6399/12500, reward: 0.848, revenue_rate: 0.848, distance: 13.539, power: 0.1118, loss: 1.3315, lr:0.0000131, took: 218.0577s
Batch 6499/12500, reward: 0.853, revenue_rate: 0.853, distance: 13.638, power: 0.1128, loss: 0.4573, lr:0.0000131, took: 218.1460s
Batch 6599/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.785, power: 0.1160, loss: 0.2396, lr:0.0000131, took: 218.9019s
Batch 6699/12500, reward: 0.851, revenue_rate: 0.851, distance: 13.645, power: 0.1156, loss: 1.3645, lr:0.0000131, took: 218.5979s
Batch 6799/12500, reward: 0.854, revenue_rate: 0.854, distance: 13.716, power: 0.1127, loss: 0.9300, lr:0.0000131, took: 217.0104s
Batch 6899/12500, reward: 0.854, revenue_rate: 0.854, distance: 13.700, power: 0.1149, loss: 0.5556, lr:0.0000131, took: 218.4381s
Batch 6999/12500, reward: 0.849, revenue_rate: 0.849, distance: 13.553, power: 0.1142, loss: 1.8649, lr:0.0000105, took: 218.8565s
Batch 7099/12500, reward: 0.849, revenue_rate: 0.849, distance: 13.692, power: 0.1138, loss: 0.5517, lr:0.0000105, took: 218.8913s
Batch 7199/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.706, power: 0.1162, loss: 1.9014, lr:0.0000105, took: 218.7256s
Batch 7299/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.720, power: 0.1179, loss: 0.7786, lr:0.0000105, took: 219.5799s
Batch 7399/12500, reward: 0.847, revenue_rate: 0.847, distance: 13.568, power: 0.1133, loss: 0.6317, lr:0.0000105, took: 219.2275s
Batch 7499/12500, reward: 0.853, revenue_rate: 0.853, distance: 13.637, power: 0.1180, loss: 0.1540, lr:0.0000105, took: 219.1546s
Batch 7599/12500, reward: 0.852, revenue_rate: 0.852, distance: 13.666, power: 0.1122, loss: 1.0211, lr:0.0000105, took: 218.6311s
Batch 7699/12500, reward: 0.852, revenue_rate: 0.852, distance: 13.712, power: 0.1161, loss: 0.7682, lr:0.0000105, took: 218.6451s
Batch 7799/12500, reward: 0.868, revenue_rate: 0.868, distance: 13.920, power: 0.1232, loss: -1.4358, lr:0.0000105, took: 219.3715s
Batch 7899/12500, reward: 0.854, revenue_rate: 0.854, distance: 13.681, power: 0.1176, loss: 0.3173, lr:0.0000105, took: 218.8715s
Batch 7999/12500, reward: 0.851, revenue_rate: 0.851, distance: 13.645, power: 0.1137, loss: -0.5628, lr:0.0000084, took: 218.8724s
Batch 8099/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.741, power: 0.1168, loss: -0.7843, lr:0.0000084, took: 218.7709s
Batch 8199/12500, reward: 0.863, revenue_rate: 0.863, distance: 13.905, power: 0.1185, loss: 0.7113, lr:0.0000084, took: 220.1893s
Batch 8299/12500, reward: 0.853, revenue_rate: 0.853, distance: 13.652, power: 0.1108, loss: -1.7592, lr:0.0000084, took: 217.6380s
Batch 8399/12500, reward: 0.854, revenue_rate: 0.854, distance: 13.680, power: 0.1153, loss: -0.3830, lr:0.0000084, took: 219.2934s
Batch 8499/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.733, power: 0.1139, loss: -0.2904, lr:0.0000084, took: 218.5624s
Batch 8599/12500, reward: 0.861, revenue_rate: 0.861, distance: 13.813, power: 0.1152, loss: 0.6431, lr:0.0000084, took: 218.8271s
Batch 8699/12500, reward: 0.853, revenue_rate: 0.853, distance: 13.693, power: 0.1141, loss: 0.0170, lr:0.0000084, took: 218.5630s
Batch 8799/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.704, power: 0.1170, loss: 0.6799, lr:0.0000084, took: 219.2054s
Batch 8899/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.835, power: 0.1154, loss: 0.8667, lr:0.0000084, took: 219.9262s
Batch 8999/12500, reward: 0.849, revenue_rate: 0.849, distance: 13.670, power: 0.1158, loss: -0.5810, lr:0.0000067, took: 219.4514s
Batch 9099/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.778, power: 0.1184, loss: 0.3599, lr:0.0000067, took: 219.1593s
Batch 9199/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.775, power: 0.1160, loss: -0.6711, lr:0.0000067, took: 218.9710s
Batch 9299/12500, reward: 0.847, revenue_rate: 0.847, distance: 13.656, power: 0.1126, loss: 1.1203, lr:0.0000067, took: 219.1895s
Batch 9399/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.868, power: 0.1173, loss: -0.1013, lr:0.0000067, took: 220.0944s
Batch 9499/12500, reward: 0.858, revenue_rate: 0.858, distance: 13.820, power: 0.1189, loss: 0.3551, lr:0.0000067, took: 218.9004s
Batch 9599/12500, reward: 0.852, revenue_rate: 0.852, distance: 13.797, power: 0.1147, loss: 1.0314, lr:0.0000067, took: 220.0858s
Batch 9699/12500, reward: 0.850, revenue_rate: 0.850, distance: 13.668, power: 0.1136, loss: 0.9105, lr:0.0000067, took: 219.2781s
Batch 9799/12500, reward: 0.850, revenue_rate: 0.850, distance: 13.636, power: 0.1135, loss: 0.1369, lr:0.0000067, took: 220.2173s
Batch 9899/12500, reward: 0.850, revenue_rate: 0.850, distance: 13.694, power: 0.1143, loss: 0.6996, lr:0.0000067, took: 218.5864s
Batch 9999/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.712, power: 0.1178, loss: 1.0165, lr:0.0000054, took: 218.3842s
Batch 10099/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.737, power: 0.1160, loss: 0.9707, lr:0.0000054, took: 219.9029s
Batch 10199/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.794, power: 0.1182, loss: 0.8732, lr:0.0000054, took: 219.5177s
Batch 10299/12500, reward: 0.861, revenue_rate: 0.861, distance: 13.826, power: 0.1182, loss: -1.0985, lr:0.0000054, took: 220.0933s
Batch 10399/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.815, power: 0.1160, loss: -0.0655, lr:0.0000054, took: 219.4637s
Batch 10499/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.861, power: 0.1152, loss: -0.1242, lr:0.0000054, took: 220.0961s
Batch 10599/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.905, power: 0.1168, loss: 0.2594, lr:0.0000054, took: 219.9022s
Batch 10699/12500, reward: 0.858, revenue_rate: 0.858, distance: 13.807, power: 0.1172, loss: 0.3759, lr:0.0000054, took: 219.9111s
Batch 10799/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.788, power: 0.1163, loss: -0.9740, lr:0.0000054, took: 220.1058s
Batch 10899/12500, reward: 0.858, revenue_rate: 0.858, distance: 13.754, power: 0.1166, loss: 0.7338, lr:0.0000054, took: 219.5857s
Batch 10999/12500, reward: 0.861, revenue_rate: 0.861, distance: 13.892, power: 0.1195, loss: 0.2447, lr:0.0000043, took: 219.7926s
Batch 11099/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.774, power: 0.1141, loss: 0.4335, lr:0.0000043, took: 218.8126s
Batch 11199/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.846, power: 0.1178, loss: -1.1460, lr:0.0000043, took: 220.1266s
Batch 11299/12500, reward: 0.853, revenue_rate: 0.853, distance: 13.687, power: 0.1137, loss: -0.3949, lr:0.0000043, took: 219.9423s
Batch 11399/12500, reward: 0.854, revenue_rate: 0.854, distance: 13.747, power: 0.1158, loss: 0.5208, lr:0.0000043, took: 218.8058s
Batch 11499/12500, reward: 0.858, revenue_rate: 0.858, distance: 13.795, power: 0.1164, loss: -0.4954, lr:0.0000043, took: 220.3710s
Batch 11599/12500, reward: 0.851, revenue_rate: 0.851, distance: 13.706, power: 0.1164, loss: -0.1063, lr:0.0000043, took: 218.9869s
Batch 11699/12500, reward: 0.856, revenue_rate: 0.856, distance: 13.762, power: 0.1137, loss: -0.0351, lr:0.0000043, took: 219.8903s
Batch 11799/12500, reward: 0.861, revenue_rate: 0.861, distance: 13.827, power: 0.1184, loss: -0.5559, lr:0.0000043, took: 219.4433s
Batch 11899/12500, reward: 0.857, revenue_rate: 0.857, distance: 13.720, power: 0.1160, loss: -0.9235, lr:0.0000043, took: 219.5085s
Batch 11999/12500, reward: 0.860, revenue_rate: 0.860, distance: 13.905, power: 0.1174, loss: -0.2333, lr:0.0000034, took: 220.1650s
Batch 12099/12500, reward: 0.859, revenue_rate: 0.859, distance: 13.808, power: 0.1131, loss: -0.1978, lr:0.0000034, took: 219.0483s
Batch 12199/12500, reward: 0.855, revenue_rate: 0.855, distance: 13.813, power: 0.1163, loss: -0.5465, lr:0.0000034, took: 219.8673s
Batch 12299/12500, reward: 0.861, revenue_rate: 0.861, distance: 13.789, power: 0.1170, loss: -0.5323, lr:0.0000034, took: 219.9646s
Batch 12399/12500, reward: 0.863, revenue_rate: 0.863, distance: 13.857, power: 0.1180, loss: -0.0886, lr:0.0000034, took: 220.0682s
Batch 12499/12500, reward: 0.864, revenue_rate: 0.864, distance: 13.838, power: 0.1211, loss: 1.3603, lr:0.0000034, took: 220.6409s
Epoch 0 mean epoch loss/reward: 0.0783, 0.7468, -0.8706, took: 18250.9753s (132.0125s / 100 batches)
